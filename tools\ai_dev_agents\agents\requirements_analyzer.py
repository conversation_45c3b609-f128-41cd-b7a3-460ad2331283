"""
Improved Requirements Analyzer Agent for generating detailed user stories.
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext
from ..core.xml_schemas import XMLParser, UserStory


class RequirementsAnalyzerAgent(BaseAgent):
    """Agent responsible for analyzing requirements and generating detailed user stories."""
    
    def __init__(self, llm_client, verbose: bool = False, streaming: bool = False):
        """Initialize the improved requirements analyzer agent."""
        super().__init__("improved_requirements_analyzer", llm_client, verbose=verbose, streaming=streaming)
        self.agent_name = "improved_requirements_analyzer"

        # Load system prompt
        self.system_prompt = self._load_system_prompt()
    
    def _load_system_prompt(self) -> str:
        """Load system prompt for requirements analysis."""
        return """你是一位资深的需求分析师，专门负责基于业务分析和领域建模结果生成详细的用户故事。

你的任务是：
1. 基于业务分析结果深入理解业务需求
2. 结合领域建模的技术架构设计
3. 将功能需求拆分为具体的用户故事
4. 按照领域上下文对用户故事进行分组
5. 确保用户故事符合INVEST原则

INVEST原则：
- Independent（独立性）：用户故事之间相互独立
- Negotiable（可协商）：用户故事的细节可以协商
- Valuable（有价值）：用户故事对用户有明确价值
- Estimable（可估算）：用户故事的工作量可以估算
- Small（小）：用户故事足够小，可以在一个迭代内完成
- Testable（可测试）：用户故事有明确的验收标准

输出格式要求：
请严格按照以下XML格式输出用户故事：

<user_stories_analysis generated_at="2024-01-01T00:00:00">
    <domain_contexts>
        <context name="用户管理">
            <description>用户相关的业务功能</description>
            <stories>
                <story id="US-001" priority="high">
                    <title>用户故事标题</title>
                    <description>作为[角色]，我希望[功能]，以便[价值]</description>
                    <acceptance_criteria>
                        <criterion>验收标准1</criterion>
                        <criterion>验收标准2</criterion>
                    </acceptance_criteria>
                    <business_value>业务价值说明</business_value>
                    <technical_notes>技术实现要点</technical_notes>
                </story>
            </stories>
        </context>
    </domain_contexts>
    <story_dependencies>
        <dependency from="US-001" to="US-002" type="prerequisite">依赖关系说明</dependency>
    </story_dependencies>
</user_stories_analysis>

请确保：
- 用户故事ID连续编号（US-001, US-002等）
- 每个用户故事都有明确的角色、功能和价值
- 验收标准具体可测试
- 按照领域上下文合理分组
- 考虑用户故事之间的依赖关系
- 优先级设置合理（high, medium, low）"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process business analysis and domain model to generate user stories."""
        try:
            self.logger.info("Starting improved requirements analysis")
            
            # Extract input data
            business_analysis = input_data.get("business_analysis", {})
            domain_model = input_data.get("domain_model", {})
            improvement_suggestions = input_data.get("improvement_suggestions", [])
            is_regeneration = "original_requirements" in input_data
            
            if not business_analysis:
                return AgentResult(
                    success=False,
                    errors=["No business analysis data provided"],
                    data={}
                )
            
            self.logger.info(f"Analyzing requirements (regeneration: {is_regeneration})")
            
            # Generate requirements prompt
            requirements_prompt = self._generate_requirements_prompt(
                business_analysis, domain_model, improvement_suggestions, is_regeneration
            )
            
            # Get LLM response
            requirements_response = self._get_llm_response(requirements_prompt)
            if not requirements_response:
                return AgentResult(
                    success=False,
                    errors=["Failed to get LLM response for requirements analysis"],
                    data={}
                )
            
            # Parse XML response
            user_stories_data = self._parse_requirements_response(requirements_response)
            
            # Save requirements to file
            if hasattr(context, 'output_path'):
                self._save_requirements_result(user_stories_data, requirements_response, context.output_path)
            
            self.logger.info(f"Requirements analysis completed. Generated {len(user_stories_data.get('user_stories', []))} user stories")
            
            return AgentResult(
                success=True,
                data=user_stories_data,
                metadata={
                    "analysis_timestamp": datetime.now().isoformat(),
                    "user_stories_count": len(user_stories_data.get("user_stories", [])),
                    "domain_contexts_count": len(user_stories_data.get("domain_contexts", [])),
                    "is_regeneration": is_regeneration
                }
            )
            
        except Exception as e:
            self.logger.error(f"Requirements analysis failed: {e}")
            return AgentResult(
                success=False,
                errors=[str(e)],
                data={}
            )
    
    def _generate_requirements_prompt(self, business_analysis: Dict[str, Any], 
                                    domain_model: Dict[str, Any],
                                    improvement_suggestions: List[Dict[str, Any]],
                                    is_regeneration: bool) -> str:
        """Generate prompt for requirements analysis."""
        
        # Prepare business analysis summary
        business_summary = f"""
项目名称：{business_analysis.get('project_name', '未知项目')}
项目描述：{business_analysis.get('project_description', '无描述')}

项目目标：
"""
        for obj in business_analysis.get('objectives', []):
            business_summary += f"- {obj}\n"
        
        business_summary += "\n功能需求：\n"
        for req in business_analysis.get('functional_requirements', []):
            business_summary += f"- {req.get('id', '')}: {req.get('title', '')}\n"
        
        # Prepare domain model summary
        domain_summary = ""
        if domain_model:
            domain_summary = f"""
领域模型信息：
{json.dumps(domain_model, ensure_ascii=False, indent=2)}
"""
        
        # Prepare improvement context
        improvement_context = ""
        if is_regeneration and improvement_suggestions:
            improvement_context = """
这是基于质量审核反馈的重新生成，请特别关注以下改进建议：
"""
            for suggestion in improvement_suggestions:
                if isinstance(suggestion, dict):
                    priority = suggestion.get('priority', 'medium')
                    text = suggestion.get('text', str(suggestion))
                    improvement_context += f"- [{priority}] {text}\n"
                else:
                    improvement_context += f"- {suggestion}\n"
            improvement_context += "\n"
        
        prompt = f"""请基于以下信息生成详细的用户故事：

{improvement_context}

业务分析结果：
{business_summary}

{domain_summary}

请按照系统提示中的XML格式输出详细的用户故事分析结果。确保：
1. 用户故事符合INVEST原则
2. 按照业务领域进行合理分组
3. 验收标准具体可测试
4. 考虑用户故事之间的依赖关系
5. 优先级设置合理

请直接输出XML格式的分析结果，不要包含其他说明文字。"""
        
        return prompt
    
    def _parse_requirements_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM requirements response."""
        # Extract XML from response
        xml_content = XMLParser.extract_xml_from_text(response)
        if not xml_content:
            xml_content = response.strip()
        
        try:
            root = ET.fromstring(xml_content)
            
            # Parse domain contexts and user stories
            domain_contexts = []
            all_user_stories = []
            
            contexts_elem = root.find("domain_contexts")
            if contexts_elem is not None:
                for context_elem in contexts_elem.findall("context"):
                    context_name = context_elem.get("name", "")
                    
                    desc_elem = context_elem.find("description")
                    context_description = desc_elem.text if desc_elem is not None else ""
                    
                    context_stories = []
                    stories_elem = context_elem.find("stories")
                    if stories_elem is not None:
                        for story_elem in stories_elem.findall("story"):
                            story_data = self._parse_story_element(story_elem, context_name)
                            context_stories.append(story_data)
                            all_user_stories.append(story_data)
                    
                    domain_contexts.append({
                        "name": context_name,
                        "description": context_description,
                        "stories": context_stories
                    })
            
            # Parse story dependencies
            dependencies = []
            deps_elem = root.find("story_dependencies")
            if deps_elem is not None:
                for dep_elem in deps_elem.findall("dependency"):
                    dependencies.append({
                        "from": dep_elem.get("from", ""),
                        "to": dep_elem.get("to", ""),
                        "type": dep_elem.get("type", ""),
                        "description": dep_elem.text or ""
                    })
            
            return {
                "domain_contexts": domain_contexts,
                "user_stories": all_user_stories,
                "story_dependencies": dependencies,
                "generated_at": datetime.now().isoformat()
            }
            
        except ET.ParseError as e:
            self.logger.warning(f"Failed to parse XML response: {e}")
            # Fallback: create basic structure
            return {
                "domain_contexts": [],
                "user_stories": [],
                "story_dependencies": [],
                "generated_at": datetime.now().isoformat(),
                "parse_error": str(e),
                "raw_response": response
            }
    
    def _parse_story_element(self, story_elem: ET.Element, domain_context: str) -> Dict[str, Any]:
        """Parse individual story element."""
        story_id = story_elem.get("id", "")
        priority = story_elem.get("priority", "medium")
        
        title_elem = story_elem.find("title")
        title = title_elem.text if title_elem is not None else ""
        
        desc_elem = story_elem.find("description")
        description = desc_elem.text if desc_elem is not None else ""
        
        # Parse acceptance criteria
        acceptance_criteria = []
        criteria_elem = story_elem.find("acceptance_criteria")
        if criteria_elem is not None:
            for criterion in criteria_elem.findall("criterion"):
                if criterion.text:
                    acceptance_criteria.append(criterion.text)
        
        # Parse business value
        value_elem = story_elem.find("business_value")
        business_value = value_elem.text if value_elem is not None else ""
        
        # Parse technical notes
        notes_elem = story_elem.find("technical_notes")
        technical_notes = notes_elem.text if notes_elem is not None else ""
        
        return {
            "id": story_id,
            "title": title,
            "description": description,
            "acceptance_criteria": acceptance_criteria,
            "priority": priority,
            "domain_context": domain_context,
            "business_value": business_value,
            "technical_notes": technical_notes
        }
    
    def _save_requirements_result(self, user_stories_data: Dict[str, Any], 
                                xml_response: str, output_path: Path):
        """Save requirements result to files."""
        try:
            # Save structured data
            requirements_file = output_path / "requirements_analysis.json"
            requirements_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(requirements_file, 'w', encoding='utf-8') as f:
                json.dump(user_stories_data, f, indent=2, ensure_ascii=False)
            
            # Save raw XML response
            xml_file = output_path / "requirements_analysis.xml"
            with open(xml_file, 'w', encoding='utf-8') as f:
                f.write(xml_response)
            
            self.logger.info(f"Requirements analysis saved to {requirements_file} and {xml_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save requirements analysis: {e}")
    
    def _get_llm_response(self, prompt: str) -> Optional[str]:
        """Get response from LLM with streaming support."""
        try:
            if not self.llm:
                self.logger.warning("No LLM client available")
                return None

            # Use BaseAgent's streaming method
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ]

            response = self._execute_llm_call_with_streaming(messages, "需求分析处理")

            return response

        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return None
