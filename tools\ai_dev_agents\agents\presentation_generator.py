"""
Presentation Generator Agent for creating HTML reports.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class PresentationGeneratorAgent(BaseAgent):
    """Agent responsible for generating HTML presentation of workflow results."""
    
    def __init__(self, llm_client=None, verbose: bool = False, streaming: bool = False, stream_displayer=None):
        """Initialize the presentation generator agent."""
        super().__init__(
            "presentation_generator",
            llm_client,
            verbose=verbose,
            streaming=streaming,
            stream_displayer=stream_displayer
        )
        self.agent_name = "presentation_generator"
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Generate HTML presentation from workflow results."""
        try:
            self.logger.info("Starting HTML presentation generation")
            
            # Extract workflow results
            workflow_results = input_data.get("workflow_results", {})
            execution_context = input_data.get("execution_context", {})
            agent_results = input_data.get("agent_results", {})
            
            # Generate HTML content
            html_content = self._generate_html_report(workflow_results, execution_context, agent_results)
            
            # Save HTML file
            if hasattr(context, 'output_path'):
                html_file = self._save_html_report(html_content, context.output_path)
            else:
                html_file = "report.html"
            
            self.logger.info(f"HTML presentation generated: {html_file}")
            
            return AgentResult(
                success=True,
                data={
                    "html_file": str(html_file),
                    "html_content": html_content,
                    "report_sections": ["overview", "business_analysis", "domain_model", "user_stories", "quality_review"]
                },
                metadata={
                    "generation_timestamp": datetime.now().isoformat(),
                    "total_sections": 5
                }
            )
            
        except Exception as e:
            self.logger.error(f"HTML presentation generation failed: {e}")
            return AgentResult(
                success=False,
                data={},
                errors=[str(e)]
            )
    
    def _generate_html_report(self, workflow_results: Dict[str, Any], 
                            execution_context: Dict[str, Any],
                            agent_results: Dict[str, Any]) -> str:
        """Generate complete HTML report."""
        
        # Extract key data
        business_analysis = workflow_results.get("business_analysis", {})
        domain_model = workflow_results.get("domain_model", {})
        requirements = workflow_results.get("requirements", {})
        quality_review = workflow_results.get("quality_review", {})
        
        # Generate sections
        overview_section = self._generate_overview_section(workflow_results, execution_context)
        business_section = self._generate_business_analysis_section(business_analysis)
        domain_section = self._generate_domain_model_section(domain_model)
        requirements_section = self._generate_requirements_section(requirements)
        quality_section = self._generate_quality_review_section(quality_review)
        
        # Combine into full HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI开发工作流报告</title>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>AI开发工作流报告</h1>
            <p class="subtitle">自动化需求分析与用户故事生成</p>
            <p class="timestamp">生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </header>
        
        <nav class="navigation">
            <a href="#overview">概览</a>
            <a href="#business">业务分析</a>
            <a href="#domain">领域建模</a>
            <a href="#requirements">需求分析</a>
            <a href="#quality">质量审核</a>
        </nav>
        
        <main>
            {overview_section}
            {business_section}
            {domain_section}
            {requirements_section}
            {quality_section}
        </main>
        
        <footer>
            <p>由AI开发工作流系统自动生成</p>
        </footer>
    </div>
    
    <script>
        {self._get_javascript()}
    </script>
</body>
</html>
"""
        return html_content
    
    def _generate_overview_section(self, workflow_results: Dict[str, Any], 
                                 execution_context: Dict[str, Any]) -> str:
        """Generate overview section."""
        steps_completed = execution_context.get("completed_steps", 0)
        total_steps = execution_context.get("total_steps", 6)
        start_time = execution_context.get("start_time", "")
        
        # Calculate statistics
        business_analysis = workflow_results.get("business_analysis", {})
        requirements = workflow_results.get("requirements", {})
        
        project_name = business_analysis.get("project_name", "未知项目")
        user_stories_count = len(requirements.get("user_stories", []))
        domain_contexts_count = len(requirements.get("domain_contexts", []))
        
        return f"""
        <section id="overview" class="section">
            <h2>项目概览</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>项目信息</h3>
                    <p><strong>项目名称:</strong> {project_name}</p>
                    <p><strong>分析时间:</strong> {start_time}</p>
                    <p><strong>完成步骤:</strong> {steps_completed}/{total_steps}</p>
                </div>
                <div class="overview-card">
                    <h3>生成统计</h3>
                    <p><strong>用户故事数量:</strong> {user_stories_count}</p>
                    <p><strong>领域上下文:</strong> {domain_contexts_count}</p>
                    <p><strong>功能需求:</strong> {len(business_analysis.get('functional_requirements', []))}</p>
                </div>
                <div class="overview-card">
                    <h3>质量指标</h3>
                    <p><strong>整体评分:</strong> {workflow_results.get('quality_review', {}).get('overall_score', 'N/A')}/10</p>
                    <p><strong>审核状态:</strong> {'通过' if workflow_results.get('quality_review', {}).get('approved', False) else '需改进'}</p>
                </div>
            </div>
        </section>
        """
    
    def _generate_business_analysis_section(self, business_analysis: Dict[str, Any]) -> str:
        """Generate business analysis section."""
        if not business_analysis:
            return '<section id="business" class="section"><h2>业务分析</h2><p>无业务分析数据</p></section>'
        
        objectives_html = ""
        for obj in business_analysis.get("objectives", []):
            objectives_html += f"<li>{obj}</li>"
        
        requirements_html = ""
        for req in business_analysis.get("functional_requirements", []):
            requirements_html += f"""
            <div class="requirement-item">
                <h4>{req.get('id', '')}: {req.get('title', '')}</h4>
                <p>{req.get('description', '')}</p>
                <span class="priority priority-{req.get('priority', 'medium')}">{req.get('priority', 'medium')}</span>
            </div>
            """
        
        return f"""
        <section id="business" class="section">
            <h2>业务分析</h2>
            <div class="business-content">
                <div class="project-info">
                    <h3>项目描述</h3>
                    <p>{business_analysis.get('project_description', '无描述')}</p>
                </div>
                
                <div class="objectives">
                    <h3>项目目标</h3>
                    <ul>{objectives_html}</ul>
                </div>
                
                <div class="functional-requirements">
                    <h3>功能需求</h3>
                    {requirements_html}
                </div>
            </div>
        </section>
        """
    
    def _generate_domain_model_section(self, domain_model: Dict[str, Any]) -> str:
        """Generate domain model section."""
        if not domain_model:
            return '<section id="domain" class="section"><h2>领域建模</h2><p>无领域建模数据</p></section>'
        
        return f"""
        <section id="domain" class="section">
            <h2>领域建模</h2>
            <div class="domain-content">
                <div class="model-data">
                    <h3>领域模型数据</h3>
                    <pre class="json-display">{json.dumps(domain_model, ensure_ascii=False, indent=2)}</pre>
                </div>
            </div>
        </section>
        """
    
    def _generate_requirements_section(self, requirements: Dict[str, Any]) -> str:
        """Generate requirements section."""
        if not requirements:
            return '<section id="requirements" class="section"><h2>需求分析</h2><p>无需求分析数据</p></section>'
        
        contexts_html = ""
        for context in requirements.get("domain_contexts", []):
            stories_html = ""
            for story in context.get("stories", []):
                criteria_html = ""
                for criterion in story.get("acceptance_criteria", []):
                    criteria_html += f"<li>{criterion}</li>"
                
                stories_html += f"""
                <div class="user-story">
                    <h5>{story.get('id', '')}: {story.get('title', '')}</h5>
                    <p class="story-description">{story.get('description', '')}</p>
                    <div class="acceptance-criteria">
                        <strong>验收标准:</strong>
                        <ul>{criteria_html}</ul>
                    </div>
                    <span class="priority priority-{story.get('priority', 'medium')}">{story.get('priority', 'medium')}</span>
                </div>
                """
            
            contexts_html += f"""
            <div class="domain-context">
                <h4>{context.get('name', '')}</h4>
                <p>{context.get('description', '')}</p>
                <div class="stories-container">
                    {stories_html}
                </div>
            </div>
            """
        
        return f"""
        <section id="requirements" class="section">
            <h2>需求分析</h2>
            <div class="requirements-content">
                <div class="domain-contexts">
                    {contexts_html}
                </div>
            </div>
        </section>
        """
    
    def _generate_quality_review_section(self, quality_review: Dict[str, Any]) -> str:
        """Generate quality review section."""
        if not quality_review:
            return '<section id="quality" class="section"><h2>质量审核</h2><p>无质量审核数据</p></section>'
        
        suggestions_html = ""
        for suggestion in quality_review.get("improvement_suggestions", []):
            if isinstance(suggestion, dict):
                priority = suggestion.get('priority', 'medium')
                text = suggestion.get('text', '')
                suggestions_html += f'<li class="suggestion priority-{priority}">[{priority}] {text}</li>'
            else:
                suggestions_html += f'<li class="suggestion">{suggestion}</li>'
        
        return f"""
        <section id="quality" class="section">
            <h2>质量审核</h2>
            <div class="quality-content">
                <div class="review-summary">
                    <h3>审核结果</h3>
                    <p><strong>整体评分:</strong> {quality_review.get('overall_score', 'N/A')}/10</p>
                    <p><strong>审核状态:</strong> {'✅ 通过' if quality_review.get('approved', False) else '❌ 需改进'}</p>
                    <p><strong>总结:</strong> {quality_review.get('summary', '无总结')}</p>
                </div>
                
                <div class="improvement-suggestions">
                    <h3>改进建议</h3>
                    <ul>{suggestions_html}</ul>
                </div>
            </div>
        </section>
        """
    
    def _get_css_styles(self) -> str:
        """Get CSS styles for the HTML report."""
        return """
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }
        .subtitle { font-size: 1.2em; margin: 10px 0; opacity: 0.9; }
        .timestamp { font-size: 0.9em; opacity: 0.8; }
        .navigation { display: flex; justify-content: center; gap: 20px; margin-bottom: 30px; }
        .navigation a { padding: 10px 20px; background: #f8f9fa; text-decoration: none; color: #495057; border-radius: 5px; transition: all 0.3s; }
        .navigation a:hover { background: #e9ecef; transform: translateY(-2px); }
        .section { margin-bottom: 40px; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section h2 { color: #495057; margin-bottom: 20px; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .overview-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .overview-card { padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .overview-card h3 { color: #495057; margin-bottom: 15px; }
        .requirement-item, .user-story { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; position: relative; }
        .priority { position: absolute; top: 10px; right: 10px; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .priority-high { background: #dc3545; color: white; }
        .priority-medium { background: #ffc107; color: #212529; }
        .priority-low { background: #28a745; color: white; }
        .json-display { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 0.9em; }
        .domain-context { margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .stories-container { margin-top: 15px; }
        .story-description { font-style: italic; margin: 10px 0; }
        .acceptance-criteria { margin: 10px 0; }
        .acceptance-criteria ul { margin-left: 20px; }
        .suggestion { margin-bottom: 10px; padding: 10px; background: #fff3cd; border-left: 4px solid #ffc107; }
        footer { text-align: center; margin-top: 40px; padding: 20px; color: #6c757d; }
        """
    
    def _get_javascript(self) -> str:
        """Get JavaScript for the HTML report."""
        return """
        // Smooth scrolling for navigation links
        document.querySelectorAll('.navigation a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        // Add active state to navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            const navLinks = document.querySelectorAll('.navigation a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        });
        """
    
    def _save_html_report(self, html_content: str, output_path: Path) -> Path:
        """Save HTML report to file."""
        html_file = output_path / "workflow_report.html"
        html_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML report saved to {html_file}")
        return html_file
