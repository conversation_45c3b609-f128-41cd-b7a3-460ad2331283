"""
Orchestrator for the 6-step AI development workflow.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from .base_agent import BaseAgent, AgentResult, WorkflowContext
from .xml_schemas import XMLParser, BusinessAnalysis, UserStory
from .workflow_logger import WorkflowLogger
from ..utils.progress_display import EnhancedProgressDisplay


class Orchestrator:
    """Orchestrator for the 6-step workflow."""

    def __init__(self, agents: Dict[str, BaseAgent], verbose: bool = False,
                 streaming_enabled: bool = False, show_progress: bool = True):
        """Initialize orchestrator with agents."""
        self.agents = agents
        self.verbose = verbose
        self.streaming_enabled = streaming_enabled
        self.show_progress = show_progress and not streaming_enabled  # Don't show progress in streaming mode

        # Initialize with default context - will be updated during execution
        self.context = WorkflowContext(
            project_root=".",
            project_rules={},
            existing_modules=[],
            tech_stack=["FastAPI", "SQLAlchemy", "Pydantic"],
            architecture_style="DDD"
        )
        self.results: Dict[str, AgentResult] = {}
        self.logger = logging.getLogger(__name__)
        self.workflow_logger = None
        self.progress_display = EnhancedProgressDisplay(enabled=self.show_progress)
        
        # Validate required agents
        required_agents = [
            "business_analyzer", 
            "domain_modeler", 
            "requirements_analyzer",
            "technical_leader",
            "result_generator", 
            "presentation_generator"
        ]
        
        for agent_name in required_agents:
            if agent_name not in agents:
                raise ValueError(f"Required agent '{agent_name}' not found")
    
    def execute_workflow(self, prd_content: str, rules_content: str, output_path: Path) -> Dict[str, Any]:
        """Execute the complete 6-step workflow with enhanced progress tracking."""
        # Initialize enhanced workflow logger
        self.workflow_logger = WorkflowLogger(output_path, self.verbose, move_system_log=True)

        self.logger.info("Starting AI development workflow")
        self.workflow_logger.log_step_start(0, "Workflow Initialization", {
            "prd_length": len(prd_content),
            "rules_length": len(rules_content),
            "output_path": str(output_path)
        })

        # Start enhanced progress display
        if self.show_progress:
            self.progress_display.start_workflow()

        # Initialize context
        self.context.prd_content = prd_content
        self.context.rules_content = rules_content
        self.context.output_path = output_path
        self.context.start_time = datetime.now()
        
        workflow_results = {
            "success": False,
            "steps_completed": 0,
            "total_steps": 6,
            "results": {},
            "errors": [],
            "execution_time": 0
        }
        
        try:
            # Step 1: Business Analysis
            self.logger.info("Step 1: Executing business analysis")
            if self.show_progress:
                self.progress_display.start_step(1)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(1, "解析PRD文档", "分析产品需求文档内容")
                self.progress_display.add_sub_task(1, "提取功能需求", "识别和分类功能性需求")
                self.progress_display.add_sub_task(1, "生成业务分析", "创建结构化业务分析报告")

            self.workflow_logger.log_step_start(1, "Business Analysis", {
                "prd_content": f"String({len(prd_content)} chars)",
                "rules_content": f"String({len(rules_content)} chars)"
            })

            business_result = self._execute_business_analysis(prd_content, rules_content)
            if not business_result.success:
                if self.show_progress:
                    self.progress_display.complete_step(1, success=False)
                self.workflow_logger.log_step_complete(1, errors=business_result.errors)
                raise Exception(f"Business analysis failed: {'; '.join(business_result.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(1, success=True)
            self.workflow_logger.log_step_complete(1, business_result.data)
            self.results["business_analyzer"] = business_result
            workflow_results["steps_completed"] = 1
            workflow_results["results"]["business_analysis"] = business_result.data
            
            # Step 2: Domain Modeling
            self.logger.info("Step 2: Executing domain modeling")
            if self.show_progress:
                self.progress_display.start_step(2)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(2, "分析业务实体", "识别核心业务实体和概念")
                self.progress_display.add_sub_task(2, "设计实体关系", "定义实体间的关联关系")
                self.progress_display.add_sub_task(2, "构建领域模型", "创建完整的领域模型图")

            self.workflow_logger.log_step_start(2, "Domain Modeling", business_result.data)

            domain_result = self._execute_domain_modeling(business_result.data)
            if not domain_result.success:
                if self.show_progress:
                    self.progress_display.complete_step(2, success=False)
                self.workflow_logger.log_step_complete(2, errors=domain_result.errors)
                raise Exception(f"Domain modeling failed: {'; '.join(domain_result.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(2, success=True)
            self.workflow_logger.log_step_complete(2, domain_result.data)
            self.results["domain_modeler"] = domain_result
            workflow_results["steps_completed"] = 2
            workflow_results["results"]["domain_model"] = domain_result.data

            # Step 3: Requirements Analysis
            self.logger.info("Step 3: Executing requirements analysis")
            if self.show_progress:
                self.progress_display.start_step(3)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(3, "分析用户需求", "基于业务分析生成用户故事")
                self.progress_display.add_sub_task(3, "定义技术需求", "确定技术实现要求")
                self.progress_display.add_sub_task(3, "生成需求文档", "创建完整的需求分析文档")

            self.workflow_logger.log_step_start(3, "Requirements Analysis", {
                "business_analysis": business_result.data,
                "domain_model": domain_result.data
            })

            requirements_result = self._execute_requirements_analysis(
                business_result.data, domain_result.data
            )
            if not requirements_result.success:
                if self.show_progress:
                    self.progress_display.complete_step(3, success=False)
                raise Exception(f"Requirements analysis failed: {'; '.join(requirements_result.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(3, success=True)
            self.results["requirements_analyzer"] = requirements_result
            workflow_results["steps_completed"] = 3
            workflow_results["results"]["requirements"] = requirements_result.data
            
            # Step 4: Quality Review (with retry mechanism)
            self.logger.info("Step 4: Executing quality review")
            if self.show_progress:
                self.progress_display.start_step(4)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(4, "质量评估", "评估需求文档的完整性和准确性")
                self.progress_display.add_sub_task(4, "技术审核", "审核技术方案的可行性")
                self.progress_display.add_sub_task(4, "优化建议", "提供改进建议和最终确认")

            quality_result, final_requirements = self._execute_quality_review_with_retry(
                requirements_result.data, max_retries=2
            )
            if not quality_result.success:
                if self.show_progress:
                    self.progress_display.complete_step(4, success=False)
                raise Exception(f"Quality review failed: {'; '.join(quality_result.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(4, success=True)
            self.results["technical_leader"] = quality_result
            workflow_results["steps_completed"] = 4
            workflow_results["results"]["quality_review"] = quality_result.data
            workflow_results["results"]["final_requirements"] = final_requirements

            # Step 5: Result Generation
            self.logger.info("Step 5: Executing result generation")
            if self.show_progress:
                self.progress_display.start_step(5)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(5, "生成开发文档", "创建详细的开发指导文档")
                self.progress_display.add_sub_task(5, "生成AI提示", "创建AI辅助开发的提示模板")
                self.progress_display.add_sub_task(5, "整理输出文件", "组织和格式化所有输出文件")

            result_generation = self._execute_result_generation(final_requirements)
            if not result_generation.success:
                if self.show_progress:
                    self.progress_display.complete_step(5, success=False)
                raise Exception(f"Result generation failed: {'; '.join(result_generation.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(5, success=True)
            self.results["result_generator"] = result_generation
            workflow_results["steps_completed"] = 5
            workflow_results["results"]["generated_results"] = result_generation.data

            # Step 6: HTML Presentation
            self.logger.info("Step 6: Generating HTML presentation")
            if self.show_progress:
                self.progress_display.start_step(6)
                # Add sub-tasks for detailed tracking
                self.progress_display.add_sub_task(6, "收集展示数据", "整理所有工作流结果数据")
                self.progress_display.add_sub_task(6, "生成HTML模板", "创建响应式HTML展示页面")
                self.progress_display.add_sub_task(6, "保存最终文件", "保存HTML文件和相关资源")

            presentation_result = self._execute_presentation_generation(workflow_results["results"])
            if not presentation_result.success:
                if self.show_progress:
                    self.progress_display.complete_step(6, success=False)
                raise Exception(f"Presentation generation failed: {'; '.join(presentation_result.errors)}")

            if self.show_progress:
                self.progress_display.complete_step(6, success=True)
            self.results["presentation_generator"] = presentation_result
            workflow_results["steps_completed"] = 6
            workflow_results["results"]["presentation"] = presentation_result.data
            
            # Mark as successful
            workflow_results["success"] = True
            if self.show_progress:
                self.progress_display.complete_workflow(success=True)
            self.logger.info("Workflow completed successfully")

        except Exception as e:
            if self.show_progress:
                self.progress_display.complete_workflow(success=False)
            self.logger.error(f"Workflow failed at step {workflow_results['steps_completed'] + 1}: {e}")
            workflow_results["errors"].append(str(e))
        
        finally:
            # Calculate execution time
            if hasattr(self.context, 'start_time'):
                execution_time = (datetime.now() - self.context.start_time).total_seconds()
                workflow_results["execution_time"] = execution_time
            
            # Save workflow summary
            self._save_workflow_summary(workflow_results, output_path)
        
        return workflow_results
    
    def _execute_business_analysis(self, prd_content: str, rules_content: str) -> AgentResult:
        """Execute business analysis step with enhanced progress tracking."""
        agent = self.agents["business_analyzer"]

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(1, "解析PRD文档")
            self.workflow_logger.log_sub_task_start(1, "解析PRD文档", {"prd_length": len(prd_content)})

        # Prepare input data
        input_data = {
            "prd_content": prd_content,
            "rules_content": rules_content,
            "output_format": "xml"
        }

        if self.show_progress:
            self.progress_display.complete_sub_task(1, "解析PRD文档", True)
            self.workflow_logger.log_sub_task_complete(1, "解析PRD文档", {"input_prepared": True})

            self.progress_display.start_sub_task(1, "提取功能需求")
            self.workflow_logger.log_sub_task_start(1, "提取功能需求", input_data)

        # Execute agent processing with streaming support
        if self.streaming_enabled and hasattr(agent, 'process_with_streaming'):
            result = agent.process_with_streaming(input_data, self.context,
                                                stream_callback=self._handle_stream_output)
        else:
            result = agent.process(input_data, self.context)

        if self.show_progress:
            self.progress_display.complete_sub_task(1, "提取功能需求", result.success)
            self.workflow_logger.log_sub_task_complete(1, "提取功能需求", {"success": result.success})

            if result.success:
                self.progress_display.start_sub_task(1, "生成业务分析")
                self.workflow_logger.log_sub_task_start(1, "生成业务分析", {"data_size": len(str(result.data))})
                self.progress_display.complete_sub_task(1, "生成业务分析", True)
                self.workflow_logger.log_sub_task_complete(1, "生成业务分析", {"completed": True})

        return result

    def _handle_stream_output(self, content: str):
        """Handle streaming output from LLM."""
        if self.show_progress:
            self.progress_display.add_stream_output(content)
        # Also log to workflow logger if in verbose mode
        if self.verbose and self.workflow_logger:
            self.workflow_logger.logger.debug(f"Stream: {content}")

    def _execute_domain_modeling(self, business_analysis: Dict[str, Any]) -> AgentResult:
        """Execute domain modeling step with enhanced progress tracking."""
        agent = self.agents["domain_modeler"]

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(2, "分析业务实体")
            self.workflow_logger.log_sub_task_start(2, "分析业务实体", business_analysis)

        # Execute agent processing with streaming support
        if self.streaming_enabled and hasattr(agent, 'process_with_streaming'):
            result = agent.process_with_streaming(business_analysis, self.context,
                                                stream_callback=self._handle_stream_output)
        else:
            result = agent.process(business_analysis, self.context)

        if self.show_progress:
            self.progress_display.complete_sub_task(2, "分析业务实体", result.success)
            self.workflow_logger.log_sub_task_complete(2, "分析业务实体", {"success": result.success})

            if result.success:
                self.progress_display.start_sub_task(2, "设计实体关系")
                self.progress_display.complete_sub_task(2, "设计实体关系", True)
                self.progress_display.start_sub_task(2, "构建领域模型")
                self.progress_display.complete_sub_task(2, "构建领域模型", True)
                self.workflow_logger.log_sub_task_complete(2, "构建领域模型", {"completed": True})

        return result
    
    def _execute_requirements_analysis(self, business_analysis: Dict[str, Any],
                                     domain_model: Dict[str, Any]) -> AgentResult:
        """Execute requirements analysis step with enhanced progress tracking."""
        agent = self.agents["requirements_analyzer"]

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(3, "分析用户需求")
            self.workflow_logger.log_sub_task_start(3, "分析用户需求", {"business_analysis": business_analysis})

        input_data = {
            "business_analysis": business_analysis,
            "domain_model": domain_model
        }

        # Execute agent processing with streaming support
        if self.streaming_enabled and hasattr(agent, 'process_with_streaming'):
            result = agent.process_with_streaming(input_data, self.context,
                                                stream_callback=self._handle_stream_output)
        else:
            result = agent.process(input_data, self.context)

        if self.show_progress:
            self.progress_display.complete_sub_task(3, "分析用户需求", result.success)
            self.workflow_logger.log_sub_task_complete(3, "分析用户需求", {"success": result.success})

            if result.success:
                self.progress_display.start_sub_task(3, "定义技术需求")
                self.progress_display.complete_sub_task(3, "定义技术需求", True)
                self.progress_display.start_sub_task(3, "生成需求文档")
                self.progress_display.complete_sub_task(3, "生成需求文档", True)
                self.workflow_logger.log_sub_task_complete(3, "生成需求文档", {"completed": True})

        return result
    
    def _execute_quality_review_with_retry(self, requirements: Dict[str, Any],
                                         max_retries: int = 2) -> tuple[AgentResult, Dict[str, Any]]:
        """Execute quality review with retry mechanism and enhanced progress tracking."""
        technical_leader = self.agents["technical_leader"]
        requirements_analyzer = self.agents["requirements_analyzer"]

        current_requirements = requirements

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(4, "质量评估")
            self.workflow_logger.log_sub_task_start(4, "质量评估", {"requirements": current_requirements})

        for attempt in range(max_retries + 1):
            self.logger.info(f"Quality review attempt {attempt + 1}")

            # Review current requirements with streaming support
            if self.streaming_enabled and hasattr(technical_leader, 'process_with_streaming'):
                review_result = technical_leader.process_with_streaming(current_requirements, self.context,
                                                                      stream_callback=self._handle_stream_output)
            else:
                review_result = technical_leader.process(current_requirements, self.context)

            if not review_result.success:
                if self.show_progress:
                    self.progress_display.complete_sub_task(4, "质量评估", False)
                return review_result, current_requirements

            # Check if requirements are approved
            review_data = review_result.data
            if review_data.get("approved", False):
                self.logger.info("Requirements approved by technical leader")
                if self.show_progress:
                    self.progress_display.complete_sub_task(4, "质量评估", True)
                    self.progress_display.start_sub_task(4, "技术审核")
                    self.progress_display.complete_sub_task(4, "技术审核", True)
                    self.progress_display.start_sub_task(4, "优化建议")
                    self.progress_display.complete_sub_task(4, "优化建议", True)
                    self.workflow_logger.log_sub_task_complete(4, "优化建议", {"approved": True})
                return review_result, current_requirements

            # If not approved and we have retries left, regenerate requirements
            if attempt < max_retries:
                self.logger.info("Requirements need improvement, regenerating...")
                if self.show_progress:
                    self.progress_display.complete_sub_task(4, "质量评估", False)
                    self.progress_display.start_sub_task(4, "技术审核")

                improvement_suggestions = review_data.get("improvement_suggestions", [])

                # Add improvement context to requirements analyzer
                regeneration_input = {
                    "original_requirements": current_requirements,
                    "improvement_suggestions": improvement_suggestions,
                    "business_analysis": self.results["business_analyzer"].data,
                    "domain_model": self.results["domain_modeler"].data
                }

                regeneration_result = requirements_analyzer.process(regeneration_input, self.context)
                if regeneration_result.success:
                    current_requirements = regeneration_result.data
                    if self.show_progress:
                        self.progress_display.complete_sub_task(4, "技术审核", True)
                        self.progress_display.start_sub_task(4, "优化建议")
                else:
                    self.logger.warning("Failed to regenerate requirements")
                    if self.show_progress:
                        self.progress_display.complete_sub_task(4, "技术审核", False)
                    break

        # Complete final sub-task
        if self.show_progress:
            self.progress_display.complete_sub_task(4, "优化建议", review_result.success)
            self.workflow_logger.log_sub_task_complete(4, "优化建议", {"final_attempt": True})

        # Return the last review result and current requirements
        return review_result, current_requirements
    
    def _execute_result_generation(self, final_requirements: Dict[str, Any]) -> AgentResult:
        """Execute result generation step with enhanced progress tracking."""
        agent = self.agents["result_generator"]

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(5, "生成开发文档")
            self.workflow_logger.log_sub_task_start(5, "生成开发文档", {"requirements": final_requirements})

        # Execute agent processing with streaming support
        if self.streaming_enabled and hasattr(agent, 'process_with_streaming'):
            result = agent.process_with_streaming(final_requirements, self.context,
                                                stream_callback=self._handle_stream_output)
        else:
            result = agent.process(final_requirements, self.context)

        if self.show_progress:
            self.progress_display.complete_sub_task(5, "生成开发文档", result.success)
            self.workflow_logger.log_sub_task_complete(5, "生成开发文档", {"success": result.success})

            if result.success:
                self.progress_display.start_sub_task(5, "生成AI提示")
                self.progress_display.complete_sub_task(5, "生成AI提示", True)
                self.progress_display.start_sub_task(5, "整理输出文件")
                self.progress_display.complete_sub_task(5, "整理输出文件", True)
                self.workflow_logger.log_sub_task_complete(5, "整理输出文件", {"completed": True})

        return result
    
    def _execute_presentation_generation(self, all_results: Dict[str, Any]) -> AgentResult:
        """Execute presentation generation step with enhanced progress tracking."""
        agent = self.agents["presentation_generator"]

        # Track sub-task progress
        if self.show_progress:
            self.progress_display.start_sub_task(6, "收集展示数据")
            self.workflow_logger.log_sub_task_start(6, "收集展示数据", {"results_count": len(all_results)})

        # Prepare comprehensive input for presentation
        presentation_input = {
            "workflow_results": all_results,
            "execution_context": {
                "start_time": self.context.start_time.isoformat() if hasattr(self.context, 'start_time') else None,
                "total_steps": 6,
                "completed_steps": len(all_results)
            },
            "agent_results": {name: result.data for name, result in self.results.items()}
        }

        if self.show_progress:
            self.progress_display.complete_sub_task(6, "收集展示数据", True)
            self.progress_display.start_sub_task(6, "生成HTML模板")
            self.workflow_logger.log_sub_task_complete(6, "收集展示数据", {"input_prepared": True})

        # Execute agent processing with streaming support
        if self.streaming_enabled and hasattr(agent, 'process_with_streaming'):
            result = agent.process_with_streaming(presentation_input, self.context,
                                                stream_callback=self._handle_stream_output)
        else:
            result = agent.process(presentation_input, self.context)

        if self.show_progress:
            self.progress_display.complete_sub_task(6, "生成HTML模板", result.success)
            self.workflow_logger.log_sub_task_complete(6, "生成HTML模板", {"success": result.success})

            if result.success:
                self.progress_display.start_sub_task(6, "保存最终文件")
                self.progress_display.complete_sub_task(6, "保存最终文件", True)
                self.workflow_logger.log_sub_task_complete(6, "保存最终文件", {"completed": True})

        return result

    def _save_workflow_summary(self, workflow_results: Dict[str, Any], output_path: Path):
        """Save workflow summary to file."""
        try:
            summary_file = output_path / "workflow_summary.json"
            summary_file.parent.mkdir(parents=True, exist_ok=True)

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_results, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"Workflow summary saved to {summary_file}")
        except Exception as e:
            self.logger.error(f"Failed to save workflow summary: {e}")

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics."""
        stats = {
            "total_agents": len(self.agents),
            "completed_steps": len(self.results),
            "success_rate": len([r for r in self.results.values() if r.success]) / len(self.results) if self.results else 0,
            "agent_performance": {}
        }

        for name, result in self.results.items():
            stats["agent_performance"][name] = {
                "success": result.success,
                "execution_time": getattr(result, 'execution_time', 0),
                "errors": result.errors if not result.success else []
            }

        return stats
