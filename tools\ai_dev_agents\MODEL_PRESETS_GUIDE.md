# 模型预设配置指南

本文档详细介绍了 AI 开发工作流工具中可用的所有模型预设配置。

## 模型分类概览

### 🏆 旗舰模型（付费）
最高性能的商业模型，适合对质量要求极高的任务。

| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `gpt4_turbo` | OpenAI GPT-4.1 | 最新旗舰，卓越推理和创作 | 复杂分析、高质量代码生成 |
| `claude_sonnet4` | Anthropic Claude Sonnet 4 | 顶级推理和分析能力 | 深度分析、逻辑推理 |
| `gemini_pro` | Google Gemini 2.5 Pro | 专业级，极强多模态能力 | 多模态任务、专业分析 |

### 🎯 高质量模型
平衡性能与成本的优质选择。

| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `high_quality` | Google Gemini 2.5 Flash | 高质量分析和推理 | 代码审查、架构设计 |
| `gemini_flash` | Google Gemini 2.5 Flash | 快速响应，超长上下文 | 大文档处理、快速分析 |

### 💰 免费模型
无成本使用的高质量开源模型。

#### 免费推理模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `qwen_qwq` | Qwen QwQ 32B | 强化学习训练的推理模型 | 逻辑推理、问题解决 |
| `deepseek_r1` | DeepSeek R1 | 思维链推理，复杂问题分析 | 复杂推理、步骤分解 |

#### 免费高质量模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `qwen3_235b` | Qwen 3 235B | 超大参数，强大理解能力 | 代码理解、文档分析 |

#### 免费专业模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `microsoft_mai` | Microsoft MAI DS R1 | 数据科学专用模型 | 数据分析、算法设计 |
| `deepseek_chimera` | DeepSeek R1T Chimera | 混合架构，平衡性能与效率 | 通用开发任务 |

#### 免费通用模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `default` | DeepSeek Chat V3 | 平衡性能和成本 | 日常开发、通用任务 |
| `deepseek_v3` | DeepSeek Chat V3 | 通用对话模型 | 代码生成、文档编写 |

### ⚡ 特殊用途模型

#### 快速响应
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `fast` | DeepSeek Chat V3 | 快速响应模式 | 快速原型、简单任务 |

#### 长文本处理
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `long_context` | Google Gemini 2.5 Flash | 长文本处理 | 大型代码库分析 |

#### 经济模式
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `economy` | DeepSeek Chat V3 | 成本优化 | 批量处理、测试 |

#### 创意模式
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `creative` | DeepSeek R1 | 深度思维和推理链 | 创新设计、架构探索 |

## 使用建议

### 按任务类型选择

**代码生成与重构**
- 高质量要求：`gpt4_turbo` 或 `claude_sonnet4`
- 平衡选择：`high_quality` 或 `qwen3_235b`
- 经济选择：`default` 或 `deepseek_v3`

**架构设计与分析**
- 复杂架构：`claude_sonnet4` 或 `gemini_pro`
- 快速分析：`gemini_flash` 或 `qwen_qwq`
- 推理分析：`deepseek_r1` 或 `qwen_qwq`

**文档处理**
- 长文档：`long_context` 或 `gemini_flash`
- 快速处理：`fast` 或 `default`
- 专业文档：`microsoft_mai` 或 `claude_sonnet4`

**数据科学任务**
- 专业分析：`microsoft_mai` 或 `gemini_pro`
- 算法设计：`qwen3_235b` 或 `claude_sonnet4`
- 快速原型：`qwen_qwq` 或 `deepseek_chimera`

### 按预算选择

**免费使用**
- 首选：`qwen3_235b`（最强免费模型）
- 推理：`qwen_qwq` 或 `deepseek_r1`
- 通用：`default` 或 `deepseek_v3`
- 专业：`microsoft_mai`

**付费使用**
- 顶级：`gpt4_turbo` 或 `claude_sonnet4`
- 高效：`gemini_flash` 或 `gemini_pro`
- 平衡：`high_quality`

## 参数说明

### Temperature（温度）
- `0.05-0.1`：精确、一致的输出（代码生成、分析）
- `0.2`：轻微创意（平衡模式）
- `0.3`：更多创意（创新设计、头脑风暴）

### Max Tokens（最大令牌数）
- `8k`：简短任务（经济模式）
- `32k`：标准任务（大多数场景）
- `66k`：复杂任务（详细分析）
- `128k+`：超长文本（大型项目分析）

## 使用示例

```bash
# 使用默认模型（免费）
python main.py

# 使用高质量免费模型
python main.py --model qwen3_235b

# 使用顶级付费模型
python main.py --model gpt4_turbo

# 使用专业数据科学模型
python main.py --model microsoft_mai

# 使用推理专用模型
python main.py --model qwen_qwq
```

## 配置自定义

如需添加新的模型预设，请编辑 `config.yaml` 文件：

```yaml
model_presets:
  your_custom_preset:
    provider: "openrouter"
    model: "your/model:name"
    temperature: 0.1
    max_tokens: 32000
    description: "您的模型描述"
    category: "自定义"
```
