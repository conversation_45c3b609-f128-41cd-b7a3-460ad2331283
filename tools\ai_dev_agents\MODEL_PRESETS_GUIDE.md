# 模型预设配置指南

本文档详细介绍了 AI 开发工作流工具中可用的所有模型预设配置。

## 🔧 最新更新

**v2.0 配置优化 (2025-01-26)**
- ✅ 统一所有模型的 temperature 为 0.05，确保输出一致性
- ✅ 删除重复的模型配置，精简至 11 个核心预设
- ✅ 简化 TUI 界面颜色，采用专业的黑白灰配色方案
- ✅ 重新分类模型，按付费/免费和用途进行组织

## 模型分类概览

### 🏆 付费旗舰模型
最高性能的商业模型，适合对质量要求极高的任务。

| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `gpt4_turbo` | OpenAI GPT-4.1 | 最新旗舰，卓越推理和创作 | 复杂分析、高质量代码生成 |
| `claude_sonnet4` | Anthropic Claude Sonnet 4 | 顶级推理和分析能力 | 深度分析、逻辑推理 |
| `gemini_pro` | Google Gemini 2.5 Pro | 专业级，极强多模态能力 | 多模态任务、专业分析 |

### 🎯 付费高质量模型
平衡性能与成本的优质选择。

| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `gemini_flash` | Google Gemini 2.5 Flash | 快速响应，超长上下文 | 大文档处理、快速分析 |

### 💰 免费模型
无成本使用的高质量开源模型。

#### 免费通用模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `default` | DeepSeek Chat V3 | 通用对话和代码生成 | 日常开发、通用任务 |

#### 免费经济模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `economy` | DeepSeek Chat V3 | 成本优化，适合简单任务 | 批量处理、测试 |

#### 免费推理模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `reasoning` | DeepSeek R1 | 思维链推理，适合复杂问题分析 | 复杂推理、步骤分解 |
| `qwen_qwq` | Qwen QwQ 32B | 强化学习训练的推理模型 | 逻辑推理、问题解决 |

#### 免费高质量模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `qwen3_235b` | Qwen 3 235B | 超大参数模型，强大的理解能力 | 代码理解、文档分析 |

#### 免费专业模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `microsoft_mai` | Microsoft MAI DS R1 | 微软开发的数据科学专用模型 | 数据分析、算法设计 |

#### 免费创新模型
| 预设名称 | 模型 | 特点 | 适用场景 |
|---------|------|------|----------|
| `deepseek_chimera` | DeepSeek R1T Chimera | 混合架构，平衡性能与效率 | 通用开发任务 |

## 使用建议

### 按任务类型选择

**代码生成与重构**
- 高质量要求：`gpt4_turbo` 或 `claude_sonnet4`
- 平衡选择：`gemini_flash` 或 `qwen3_235b`
- 经济选择：`default` 或 `economy`

**架构设计与分析**
- 复杂架构：`claude_sonnet4` 或 `gemini_pro`
- 快速分析：`gemini_flash` 或 `qwen_qwq`
- 推理分析：`reasoning` 或 `qwen_qwq`

**文档处理**
- 长文档：`gemini_flash` 或 `gemini_pro`
- 快速处理：`default` 或 `economy`
- 专业文档：`microsoft_mai` 或 `claude_sonnet4`

**数据科学任务**
- 专业分析：`microsoft_mai` 或 `gemini_pro`
- 算法设计：`qwen3_235b` 或 `claude_sonnet4`
- 快速原型：`qwen_qwq` 或 `deepseek_chimera`

### 按预算选择

**免费使用**
- 首选：`qwen3_235b`（最强免费模型）
- 推理：`qwen_qwq` 或 `reasoning`
- 通用：`default`
- 专业：`microsoft_mai`
- 经济：`economy`

**付费使用**
- 顶级：`gpt4_turbo` 或 `claude_sonnet4`
- 高效：`gemini_flash` 或 `gemini_pro`

## 参数说明

### Temperature（温度）
**统一设置为 0.05** - 确保所有模型输出的一致性和精确性
- 适合代码生成、分析、推理等需要准确性的任务
- 减少随机性，提高输出的可预测性和可重复性

### Max Tokens（最大令牌数）
- `8k`：经济模式（简短任务）
- `32k`：标准任务（大多数场景）
- `66k`：复杂任务（详细分析）
- `128k+`：超长文本（大型项目分析）
- `1M+`：超大文档处理（Gemini 系列）

## 使用示例

```bash
# 使用默认模型（免费通用）
python main.py

# 使用高质量免费模型
python main.py --model qwen3_235b

# 使用顶级付费模型
python main.py --model gpt4_turbo

# 使用专业数据科学模型
python main.py --model microsoft_mai

# 使用推理专用模型
python main.py --model reasoning

# 使用经济模式（低成本）
python main.py --model economy
```

## 配置自定义

如需添加新的模型预设，请编辑 `config.yaml` 文件：

```yaml
model_presets:
  your_custom_preset:
    provider: "openrouter"
    model: "your/model:name"
    temperature: 0.1
    max_tokens: 32000
    description: "您的模型描述"
    category: "自定义"
```
