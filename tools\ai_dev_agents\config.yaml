# AI Development Agents Configuration
# 配置文件仅包含必要的LLM配置和模型预设

# LLM 提供商配置 (不包含具体模型)
llm:
  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    timeout: 60

  # OpenAI 配置
  openai:
    api_key: "your-openai-api-key"
    base_url: "https://api.openai.com/v1"
    timeout: 60

  # Anthropic 配置
  anthropic:
    api_key: "your-anthropic-api-key"
    base_url: "https://api.anthropic.com"
    timeout: 60

# 模型预设配置 (包含具体模型和提供商关联)
model_presets:
  # 默认选择（DeepSeek V3 免费版）
  default:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 32000
    description: "DeepSeek V3 免费版 - 平衡性能和成本"
    category: "通用"

  # 高质量分析
  high_quality:
    provider: "openrouter"
    model: "google/gemini-2.5-flash-preview-05-20"
    temperature: 0.05
    max_tokens: 66000
    description: "Google Gemini 2.5 Flash - 高质量分析和推理"
    category: "高质量"

  # 创意模式
  creative:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 4000
    description: "DeepSeek R1 - 创意思维和推理链"
    category: "创意"

  # 快速响应
  fast:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.05
    max_tokens: 16000
    description: "DeepSeek V3 快速模式 - 快速响应"
    category: "快速"

  # 长文本处理
  long_context:
    provider: "openrouter"
    model: "google/gemini-2.5-flash-preview-05-20"
    temperature: 0.1
    max_tokens: 66000
    description: "Google Gemini 2.5 Flash - 长文本处理"
    category: "长文本"

  # 经济模式
  economy:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 8000
    description: "DeepSeek V3 经济模式 - 成本优化"
    category: "经济"
