# AI Development Agents Configuration
# 配置文件仅包含必要的LLM配置和模型预设

# LLM 提供商配置 (不包含具体模型)
llm:
  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    timeout: 60

  # OpenAI 配置
  openai:
    api_key: "your-openai-api-key"
    base_url: "https://api.openai.com/v1"
    timeout: 60

  # Anthropic 配置
  anthropic:
    api_key: "your-anthropic-api-key"
    base_url: "https://api.anthropic.com"
    timeout: 60

# 模型预设配置 (包含具体模型和提供商关联)
model_presets:
  # 默认选择（DeepSeek V3 免费版）
  default:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 32000
    description: "DeepSeek V3 免费版 - 平衡性能和成本"
    category: "通用"

  # 高质量分析
  high_quality:
    provider: "openrouter"
    model: "google/gemini-2.5-flash"
    temperature: 0.05
    max_tokens: 66000
    description: "Google Gemini 2.5 Flash - 高质量分析和推理"
    category: "高质量"

  # 深度推理模式
  creative:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 66000
    description: "DeepSeek R1 - 深度思维和推理链"
    category: "创意"

  # 快速响应
  fast:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.05
    max_tokens: 66000
    description: "DeepSeek V3 快速模式 - 快速响应"
    category: "快速"

  # 长文本处理
  long_context:
    provider: "openrouter"
    model: "google/gemini-2.5-flash-preview-05-20"
    temperature: 0.1
    max_tokens: 66000
    description: "Google Gemini 2.5 Flash - 长文本处理"
    category: "长文本"

  # 经济模式
  economy:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 8000
    description: "DeepSeek V3 经济模式 - 成本优化"
    category: "经济"

  # === 高端付费模型 ===

  # OpenAI GPT-4.1 - 最新旗舰模型
  gpt4_turbo:
    provider: "openrouter"
    model: "openai/gpt-4.1"
    temperature: 0.1
    max_tokens: 128000
    description: "OpenAI GPT-4.1 - 最新旗舰模型，卓越的推理和创作能力"
    category: "旗舰"

  # Anthropic Claude Sonnet 4 - 顶级推理模型
  claude_sonnet4:
    provider: "openrouter"
    model: "anthropic/claude-sonnet-4"
    temperature: 0.1
    max_tokens: 200000
    description: "Anthropic Claude Sonnet 4 - 顶级推理和分析能力"
    category: "旗舰"

  # Google Gemini 2.5 Flash - 快速高质量
  gemini_flash:
    provider: "openrouter"
    model: "google/gemini-2.5-flash"
    temperature: 0.1
    max_tokens: 1000000
    description: "Google Gemini 2.5 Flash - 快速响应，支持超长上下文"
    category: "高质量"

  # Google Gemini 2.5 Pro - 专业级模型
  gemini_pro:
    provider: "openrouter"
    model: "google/gemini-2.5-pro"
    temperature: 0.1
    max_tokens: 2000000
    description: "Google Gemini 2.5 Pro - 专业级模型，极强的多模态能力"
    category: "旗舰"

  # === 免费模型 ===

  # Qwen QwQ 32B - 免费推理模型
  qwen_qwq:
    provider: "openrouter"
    model: "qwen/qwq-32b:free"
    temperature: 0.2
    max_tokens: 32000
    description: "Qwen QwQ 32B 免费版 - 强化学习训练的推理模型"
    category: "免费推理"

  # DeepSeek R1 - 免费思维链模型
  deepseek_r1:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 66000
    description: "DeepSeek R1 免费版 - 思维链推理，适合复杂问题分析"
    category: "免费推理"

  # Qwen 3 235B - 免费大模型
  qwen3_235b:
    provider: "openrouter"
    model: "qwen/qwen3-235b-a22b:free"
    temperature: 0.1
    max_tokens: 32000
    description: "Qwen 3 235B 免费版 - 超大参数模型，强大的理解能力"
    category: "免费高质量"

  # DeepSeek R1T Chimera - 免费混合模型
  deepseek_chimera:
    provider: "openrouter"
    model: "tngtech/deepseek-r1t-chimera:free"
    temperature: 0.2
    max_tokens: 32000
    description: "DeepSeek R1T Chimera 免费版 - 混合架构，平衡性能与效率"
    category: "免费创新"

  # Microsoft MAI DS R1 - 免费微软模型
  microsoft_mai:
    provider: "openrouter"
    model: "microsoft/mai-ds-r1:free"
    temperature: 0.1
    max_tokens: 32000
    description: "Microsoft MAI DS R1 免费版 - 微软开发的数据科学专用模型"
    category: "免费专业"

  # DeepSeek Chat V3 - 免费通用模型
  deepseek_v3:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 32000
    description: "DeepSeek Chat V3 免费版 - 通用对话模型，平衡性能与成本"
    category: "免费通用"
