# AI Development Agents Configuration
# 配置文件仅包含必要的LLM配置和模型预设

# LLM 提供商配置 (不包含具体模型)
llm:
  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    timeout: 60

  # OpenAI 配置
  deepseek:
    api_key: "***********************************"
    base_url: "https://api.deepseek.com/v1"
    timeout: 60
    request_timeout: 180

# 模型预设配置 (包含具体模型和提供商关联)
model_presets:
  # === 免费模型 ===

  default:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.5
    max_tokens: 8000
    description: "DeepSeek V3 免费版"
    category: "通用"
    
  deepseek_chat:
    provider: "deepseek"
    model: "deepseek-chat"
    temperature: 0.5
    max_tokens: 8000
    description: "DeepSeek V3 官方平台"
    category: "通用"

  deepseek_r1:
    provider: "deepseek"
    model: "deepseek-reasoner"
    temperature: 0.5
    max_tokens: 8000
    description: "DeepSeek V3 官方平台"
    category: "通用"

  reasoning:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.5
    max_tokens: 160000
    description: "DeepSeek R1 免费版"
    category: "推理"

  deepseek_chimera:
    provider: "openrouter"
    model: "tngtech/deepseek-r1t-chimera:free"
    temperature: 0.5
    max_tokens: 160000
    description: "DeepSeek R1T Chimera 免费版"
    category: "推理"

  # === 付费模型 ===

  gpt4_turbo:
    provider: "openrouter"
    model: "openai/gpt-4.1"
    temperature: 0.5
    max_tokens: 1000000000000
    description: "OpenAI GPT-4.1"
    category: "旗舰"

  claude_sonnet4:
    provider: "openrouter"
    model: "anthropic/claude-sonnet-4"
    temperature: 0.05
    max_tokens: 200000
    description: "Anthropic Claude Sonnet 4"
    category: "旗舰"

  gemini_flash:
    provider: "openrouter"
    model: "google/gemini-2.5-flash"
    temperature: 0.05
    max_tokens: 1000000000000
    description: "Google Gemini 2.5 Flash"
    category: "高质量"

  gemini_pro:
    provider: "openrouter"
    model: "google/gemini-2.5-pro"
    temperature: 0.05
    max_tokens: 1000000000000
    description: "Google Gemini 2.5 Pro"
    category: "旗舰"
