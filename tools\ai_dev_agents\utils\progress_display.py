"""
Enhanced Progress Display for AI Development Workflow

Provides comprehensive progress indication with:
- Major stage progress bars
- Sub-task node tracking
- Fixed-height LLM streaming output display
- Real-time status updates
"""

from datetime import datetime
from typing import Optional, Dict, List
from enum import Enum
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, TaskID, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.layout import Layout
from rich.live import Live
from rich.align import Align


class ProgressStatus(Enum):
    """Progress status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class SubTaskNode:
    """Represents a sub-task node within a major stage."""

    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.status = ProgressStatus.NOT_STARTED
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.details: List[str] = []

    def start(self):
        """Start the sub-task."""
        self.status = ProgressStatus.IN_PROGRESS
        self.start_time = datetime.now()

    def complete(self, success: bool = True):
        """Complete the sub-task."""
        self.status = ProgressStatus.COMPLETED if success else ProgressStatus.FAILED
        self.end_time = datetime.now()

    def add_detail(self, detail: str):
        """Add a detail message to the sub-task."""
        self.details.append(f"[{datetime.now().strftime('%H:%M:%S')}] {detail}")

    @property
    def duration(self) -> Optional[float]:
        """Get the duration of the sub-task in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None


class EnhancedProgressDisplay:
    """
    Enhanced progress display for AI development workflow.

    Features:
    - Major stage progress bars
    - Sub-task node tracking
    - Fixed-height LLM streaming output display
    - Real-time status updates
    """

    # Status symbols
    STATUS_SYMBOLS = {
        ProgressStatus.NOT_STARTED: '○',
        ProgressStatus.IN_PROGRESS: '●',
        ProgressStatus.COMPLETED: '✓',
        ProgressStatus.FAILED: '✗',
        ProgressStatus.SKIPPED: '○'
    }

    # Stage definitions
    WORKFLOW_STAGES = [
        {"name": "业务分析", "description": "分析PRD文档，提取业务需求"},
        {"name": "领域建模", "description": "构建领域模型和实体关系"},
        {"name": "需求分析", "description": "生成用户故事和技术需求"},
        {"name": "质量审核", "description": "技术评审和质量检查"},
        {"name": "结果生成", "description": "生成最终技术文档"},
        {"name": "HTML展示", "description": "生成可视化展示页面"}
    ]
    
    def __init__(self, total_steps: int = 6, enabled: bool = True, stream_height: int = 20, model_info: str = ""):
        """
        Initialize enhanced progress display.

        Args:
            total_steps: Total number of workflow steps
            enabled: Whether to enable progress display
            stream_height: Height of the LLM streaming output area
            model_info: Current model description to display in header
        """
        self.total_steps = total_steps
        self.enabled = enabled
        self.stream_height = stream_height
        self.model_description = model_info  # Store as model_description instead of model_info

        # Progress tracking
        self.current_step = 0
        self.start_time = None
        self.steps_status = [ProgressStatus.NOT_STARTED] * total_steps
        self.sub_tasks: Dict[int, List[SubTaskNode]] = {}

        # LLM streaming output
        self.stream_buffer: List[str] = []
        self.max_stream_lines = stream_height * 3  # Allow more content for scrolling effect
        self.stream_scroll_position = 0

        # Workflow completion tracking
        self.workflow_completed = False
        self.workflow_success = False
        self.completion_summary = ""

        # Rich components
        self.console = Console()
        self.layout = Layout()
        self.progress = Progress(
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=40),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        )
        self.live = None
        self.stage_tasks: Dict[int, TaskID] = {}

        # Initialize layout
        self._setup_layout()

    def _setup_layout(self):
        """Setup the Rich layout structure."""
        # Reset layout to avoid conflicts when reconfiguring
        self.layout = Layout()

        if self.workflow_completed:
            # Show completion summary layout
            self.layout.split_column(
                Layout(name="header", size=3),
                Layout(name="summary", size=10),
                Layout(name="main"),
                Layout(name="stream", size=self.stream_height + 2)
            )
            self.layout["main"].split_row(
                Layout(name="progress", ratio=3),
                Layout(name="details", ratio=2)
            )
        else:
            # Normal workflow layout
            self.layout.split_column(
                Layout(name="header", size=3),
                Layout(name="main"),
                Layout(name="stream", size=self.stream_height + 2)
            )
            self.layout["main"].split_row(
                Layout(name="progress", ratio=3),
                Layout(name="details", ratio=2)
            )

    def start_workflow(self):
        """Start workflow progress tracking."""
        if not self.enabled:
            return

        self.start_time = datetime.now()

        # Initialize stage progress bars
        for i, stage in enumerate(self.WORKFLOW_STAGES):
            task_id = self.progress.add_task(
                f"步骤 {i+1}: {stage['name']}",
                total=100,
                completed=0
            )
            self.stage_tasks[i+1] = task_id

        # Start live display
        self._start_live_display()

    def start_step(self, step_number: int, step_name: str = "", description: str = ""):
        """Start a workflow step."""
        if not self.enabled:
            return

        self.current_step = step_number

        if step_number <= len(self.steps_status):
            self.steps_status[step_number - 1] = ProgressStatus.IN_PROGRESS

        # Update progress bar
        if step_number in self.stage_tasks:
            self.progress.update(self.stage_tasks[step_number], completed=10)

        self._update_display()

    def add_sub_task(self, step_number: int, task_name: str, description: str = ""):
        """Add a sub-task to a workflow step."""
        if not self.enabled:
            return

        if step_number not in self.sub_tasks:
            self.sub_tasks[step_number] = []

        sub_task = SubTaskNode(task_name, description)
        self.sub_tasks[step_number].append(sub_task)
        return sub_task

    def start_sub_task(self, step_number: int, task_name: str):
        """Start a specific sub-task."""
        if not self.enabled or step_number not in self.sub_tasks:
            return

        for sub_task in self.sub_tasks[step_number]:
            if sub_task.name == task_name:
                sub_task.start()
                self._update_display()
                break

    def complete_sub_task(self, step_number: int, task_name: str, success: bool = True, detail: str = ""):
        """Complete a specific sub-task."""
        if not self.enabled or step_number not in self.sub_tasks:
            return

        for sub_task in self.sub_tasks[step_number]:
            if sub_task.name == task_name:
                sub_task.complete(success)
                if detail:
                    sub_task.add_detail(detail)
                self._update_display()
                break

    def complete_step(self, step_number: int, success: bool = True):
        """Complete a workflow step."""
        if not self.enabled:
            return

        if step_number <= len(self.steps_status):
            self.steps_status[step_number - 1] = ProgressStatus.COMPLETED if success else ProgressStatus.FAILED

        # Update progress bar to 100%
        if step_number in self.stage_tasks:
            self.progress.update(self.stage_tasks[step_number], completed=100)

        # Complete all sub-tasks for this step
        if step_number in self.sub_tasks:
            for sub_task in self.sub_tasks[step_number]:
                if sub_task.status == ProgressStatus.IN_PROGRESS:
                    sub_task.complete(success)

        self._update_display()

    def add_stream_output(self, content: str):
        """Add content to the LLM streaming output display."""
        if not self.enabled:
            return

        # Add content directly to buffer without timestamp formatting
        # This preserves the natural flow of AI-generated content
        if content:
            # If buffer is empty or last entry doesn't end with newline, append to last line
            if self.stream_buffer and not self.stream_buffer[-1].endswith('\n'):
                self.stream_buffer[-1] += content
            else:
                self.stream_buffer.append(content)

            # Split into lines if content contains newlines
            if '\n' in content:
                # Split the last entry by newlines
                last_content = self.stream_buffer[-1]
                lines = last_content.split('\n')
                # Replace the last entry with split lines
                self.stream_buffer = self.stream_buffer[:-1] + lines

        # Keep only the last N lines
        if len(self.stream_buffer) > self.max_stream_lines:
            self.stream_buffer = self.stream_buffer[-self.max_stream_lines:]

        self._update_display()

    def clear_stream_output(self):
        """Clear the LLM streaming output display."""
        if not self.enabled:
            return

        self.stream_buffer.clear()
        self._update_display()

    def complete_workflow(self, success: bool = True, summary: str = ""):
        """Complete the entire workflow."""
        if not self.enabled:
            return

        # Set completion state
        self.workflow_completed = True
        self.workflow_success = success
        self.completion_summary = summary or self._generate_default_summary()

        # Stop current live display before reconfiguring layout
        if self.live:
            self.live.stop()
            self.live = None

        # Update layout to show summary
        self._setup_layout()

        # Restart live display with new layout
        self._start_live_display()
        self._update_display()

        # Keep display active for a few seconds to show completion
        import time
        time.sleep(3)

        # Stop live display
        if self.live:
            self.live.stop()

        # Show final summary in console
        total_elapsed = self._get_total_elapsed_time()

        if success:
            self.console.print(f"\n[bold green]🎉 工作流完成! 总耗时: {total_elapsed}[/bold green]")
            self.console.print(f"[green]{self.completion_summary}[/green]")
        else:
            self.console.print(f"\n[bold red]❌ 工作流失败! 总耗时: {total_elapsed}[/bold red]")
            self.console.print(f"[red]{self.completion_summary}[/red]")

    def _start_live_display(self):
        """Start the live Rich display."""
        if not self.enabled:
            return

        self.live = Live(
            self.layout,
            console=self.console,
            refresh_per_second=4,
            screen=True
        )
        self.live.start()
        self._update_display()

    def _update_display(self):
        """Update the Rich display layout."""
        if not self.enabled or not self.live:
            return

        # Update header with model info (centered)
        base_text = f"🚀 AI开发工作流 | 总耗时: {self._get_total_elapsed_time()}"
        if self.model_description:
            header_text = f"[bold cyan]{base_text}[/bold cyan] | [bold yellow]{self.model_description}[/bold yellow]"
        else:
            header_text = f"[bold cyan]{base_text}[/bold cyan]"

        self.layout["header"].update(
            Panel(
                Align.center(header_text),
                style="bright_black"
            )
        )

        # Update summary section if workflow completed
        if self.workflow_completed:
            try:
                self.layout["summary"].update(self._create_summary_panel())
            except KeyError:
                # Summary section doesn't exist, skip updating it
                pass

        # Update progress section
        self.layout["progress"].update(self.progress)

        # Update details section
        self.layout["details"].update(self._create_details_panel())

        # Update stream section
        self.layout["stream"].update(self._create_stream_panel())

    def _create_details_panel(self) -> Panel:
        """Create the details panel showing sub-tasks with enhanced visualization."""
        if self.current_step == 0 or self.current_step not in self.sub_tasks:
            return Panel(
                "[dim]等待子任务...[/dim]",
                title="[bold white]当前步骤详情[/bold white]",
                border_style="bright_black"
            )

        # Create a more visual progress display for sub-tasks
        content = []
        for i, sub_task in enumerate(self.sub_tasks[self.current_step]):
            # Status with color coding
            if sub_task.status == ProgressStatus.COMPLETED:
                status_display = "[green]●[/green]"
                task_style = "[green]"
            elif sub_task.status == ProgressStatus.IN_PROGRESS:
                status_display = "[yellow]●[/yellow]"
                task_style = "[yellow]"
            elif sub_task.status == ProgressStatus.FAILED:
                status_display = "[red]●[/red]"
                task_style = "[red]"
            else:
                status_display = "[dim]○[/dim]"
                task_style = "[dim]"

            # Duration calculation
            duration_str = ""
            if sub_task.duration:
                duration_str = f"({sub_task.duration:.1f}s)"
            elif sub_task.start_time:
                elapsed = (datetime.now() - sub_task.start_time).total_seconds()
                duration_str = f"({elapsed:.1f}s)"

            # Create progress line with connecting elements
            connector = "├─" if i < len(self.sub_tasks[self.current_step]) - 1 else "└─"
            line = f"{connector} {status_display} {task_style}{sub_task.name}[/{task_style.strip('[]')}] {duration_str}"
            content.append(line)

        return Panel(
            "\n".join(content),
            title=f"[bold white]步骤 {self.current_step} 详情[/bold white]",
            border_style="bright_black"
        )

    def _create_stream_panel(self) -> Panel:
        """Create the LLM streaming output panel with scrolling effect."""
        if not self.stream_buffer:
            content = "[dim]等待AI生成内容...[/dim]"
        else:
            # Show the most recent content with scrolling effect
            # Calculate visible lines based on stream_height
            visible_lines = self.stream_height

            # Split content into lines for better control
            all_lines = []
            for item in self.stream_buffer:
                if '\n' in item:
                    all_lines.extend(item.split('\n'))
                else:
                    all_lines.append(item)

            # Remove empty lines at the end
            while all_lines and not all_lines[-1].strip():
                all_lines.pop()

            # Show the last N lines to create scrolling effect
            if len(all_lines) > visible_lines:
                visible_content = all_lines[-visible_lines:]
                # Add scroll indicator
                content = "...\n" + "\n".join(visible_content)
            else:
                content = "\n".join(all_lines)

            # Limit total character length to prevent performance issues
            if len(content) > 3000:
                content = "..." + content[-2997:]

        # Add scroll indicator in title if there's more content
        title = "[bold white]AI 生成内容"
        if len(self.stream_buffer) > self.stream_height:
            title += " (滚动显示)"
        title += "[/bold white]"

        return Panel(
            content,
            title=title,
            border_style="bright_black"
        )

    def _create_summary_panel(self) -> Panel:
        """Create the workflow completion summary panel."""
        if self.workflow_success:
            icon = "🎉"
            status_text = "[bold green]工作流执行成功![/bold green]"
            border_style = "green"
        else:
            icon = "❌"
            status_text = "[bold red]工作流执行失败![/bold red]"
            border_style = "red"

        summary_content = f"{icon} {status_text}\n\n"
        summary_content += f"[bold]总耗时:[/bold] {self._get_total_elapsed_time()}\n"
        summary_content += f"[bold]完成步骤:[/bold] {sum(1 for status in self.steps_status if status == ProgressStatus.COMPLETED)}/{self.total_steps}\n\n"

        if self.completion_summary:
            summary_content += f"[bold]执行摘要:[/bold]\n{self.completion_summary}"

        return Panel(
            summary_content,
            title="[bold white]工作流完成总结[/bold white]",
            border_style=border_style
        )

    def _generate_default_summary(self) -> str:
        """Generate a default completion summary."""
        completed_steps = sum(1 for status in self.steps_status if status == ProgressStatus.COMPLETED)

        if self.workflow_success:
            summary = f"✅ 成功完成所有 {completed_steps} 个工作流步骤\n"
            summary += "✅ 业务分析、领域建模、需求分析等步骤均已完成\n"
            summary += "✅ 生成的技术文档和HTML展示页面已就绪"
        else:
            summary = f"⚠️  工作流在第 {completed_steps + 1} 步中断\n"
            summary += f"✅ 已完成 {completed_steps}/{self.total_steps} 个步骤\n"
            summary += "❌ 请检查错误日志并重新执行"

        return summary

    def _get_total_elapsed_time(self) -> str:
        """Get total elapsed time as formatted string."""
        if not self.start_time:
            return "0s"

        elapsed = datetime.now() - self.start_time
        total_seconds = int(elapsed.total_seconds())

        if total_seconds < 60:
            return f"{total_seconds}s"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            return f"{minutes}m {seconds}s"
        else:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return f"{hours}h {minutes}m"


# Keep the original ProgressDisplay class for backward compatibility
class ProgressDisplay(EnhancedProgressDisplay):
    """Backward compatibility alias for the enhanced progress display."""

    def __init__(self, total_steps: int = 6, enabled: bool = True):
        super().__init__(total_steps, enabled, stream_height=8)
