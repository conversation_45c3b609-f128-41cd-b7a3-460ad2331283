"""
Progress Display for AI Development Workflow

Provides user-friendly progress indication for non-streaming mode,
showing workflow execution progress, current step status, and estimated completion time.
"""

import sys
import time
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from enum import Enum


class ProgressStatus(Enum):
    """Progress status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ProgressDisplay:
    """
    User-friendly progress display for workflow execution.
    
    Shows current step, progress bar, elapsed time, and estimated completion time.
    """
    
    # Progress bar characters
    PROGRESS_CHARS = {
        'filled': '█',
        'empty': '░',
        'current': '▶'
    }
    
    # Status symbols
    STATUS_SYMBOLS = {
        ProgressStatus.NOT_STARTED: '○',
        ProgressStatus.IN_PROGRESS: '●',
        ProgressStatus.COMPLETED: '✓',
        ProgressStatus.FAILED: '✗',
        ProgressStatus.SKIPPED: '○'
    }
    
    # ANSI color codes
    COLORS = {
        'reset': '\033[0m',
        'bold': '\033[1m',
        'green': '\033[92m',
        'blue': '\033[94m',
        'yellow': '\033[93m',
        'red': '\033[91m',
        'cyan': '\033[96m',
        'gray': '\033[90m'
    }
    
    def __init__(self, total_steps: int = 6, enabled: bool = True):
        """
        Initialize progress display.
        
        Args:
            total_steps: Total number of workflow steps
            enabled: Whether to enable progress display
        """
        self.total_steps = total_steps
        self.enabled = enabled
        self.current_step = 0
        self.start_time = None
        self.step_start_time = None
        self.steps_status = [ProgressStatus.NOT_STARTED] * total_steps
        self.step_names = [
            "业务分析",
            "领域建模", 
            "需求分析",
            "质量审核",
            "结果生成",
            "HTML展示"
        ]
        self.step_descriptions = {}
        self.animation_thread = None
        self.animation_running = False
        self.last_line_length = 0
        
    def start_workflow(self):
        """Start workflow progress tracking."""
        if not self.enabled:
            return
            
        self.start_time = datetime.now()
        print(f"\n{self.COLORS['bold']}{self.COLORS['cyan']}🚀 AI开发工作流启动{self.COLORS['reset']}")
        print(f"{self.COLORS['gray']}总共 {self.total_steps} 个步骤{self.COLORS['reset']}\n")
        self._display_progress()
        
    def start_step(self, step_number: int, step_name: str, description: str = ""):
        """Start a workflow step."""
        if not self.enabled:
            return
            
        self.current_step = step_number
        self.step_start_time = datetime.now()
        
        if step_number <= len(self.steps_status):
            self.steps_status[step_number - 1] = ProgressStatus.IN_PROGRESS
            
        if step_number <= len(self.step_names):
            self.step_names[step_number - 1] = step_name
            
        if description:
            self.step_descriptions[step_number] = description
            
        self._display_progress()
        self._start_animation()
        
    def complete_step(self, step_number: int, success: bool = True):
        """Complete a workflow step."""
        if not self.enabled:
            return
            
        self._stop_animation()
        
        if step_number <= len(self.steps_status):
            self.steps_status[step_number - 1] = ProgressStatus.COMPLETED if success else ProgressStatus.FAILED
            
        self._display_progress()
        
        if success:
            elapsed = self._get_step_elapsed_time()
            print(f"\n{self.COLORS['green']}✓ 步骤 {step_number} 完成 ({elapsed:.1f}s){self.COLORS['reset']}")
        else:
            print(f"\n{self.COLORS['red']}✗ 步骤 {step_number} 失败{self.COLORS['reset']}")
            
    def complete_workflow(self, success: bool = True):
        """Complete the entire workflow."""
        if not self.enabled:
            return
            
        self._stop_animation()
        
        total_elapsed = self._get_total_elapsed_time()
        
        if success:
            print(f"\n{self.COLORS['bold']}{self.COLORS['green']}🎉 工作流完成! 总耗时: {total_elapsed}{self.COLORS['reset']}")
        else:
            print(f"\n{self.COLORS['bold']}{self.COLORS['red']}❌ 工作流失败! 总耗时: {total_elapsed}{self.COLORS['reset']}")
            
    def _display_progress(self):
        """Display current progress."""
        if not self.enabled:
            return
            
        # Clear previous line
        if self.last_line_length > 0:
            print('\r' + ' ' * self.last_line_length + '\r', end='')
            
        # Progress bar
        completed_steps = sum(1 for status in self.steps_status if status == ProgressStatus.COMPLETED)
        progress_percent = (completed_steps / self.total_steps) * 100
        
        bar_width = 30
        filled_width = int((completed_steps / self.total_steps) * bar_width)
        current_width = 1 if self.current_step > 0 and self.current_step <= self.total_steps else 0
        empty_width = bar_width - filled_width - current_width
        
        progress_bar = (
            self.PROGRESS_CHARS['filled'] * filled_width +
            self.PROGRESS_CHARS['current'] * current_width +
            self.PROGRESS_CHARS['empty'] * empty_width
        )
        
        # Time information
        elapsed_time = self._get_total_elapsed_time()
        eta = self._estimate_completion_time()
        
        # Current step info
        current_step_name = ""
        if 0 < self.current_step <= len(self.step_names):
            current_step_name = self.step_names[self.current_step - 1]
            
        # Build progress line
        progress_line = (
            f"{self.COLORS['blue']}[{progress_bar}]{self.COLORS['reset']} "
            f"{progress_percent:.0f}% "
            f"({completed_steps}/{self.total_steps}) "
            f"{self.COLORS['cyan']}{current_step_name}{self.COLORS['reset']} "
            f"{self.COLORS['gray']}| {elapsed_time}"
        )
        
        if eta:
            progress_line += f" | ETA: {eta}"
            
        progress_line += f"{self.COLORS['reset']}"
        
        print(progress_line, end='', flush=True)
        self.last_line_length = len(progress_line) - len(self.COLORS['blue']) - len(self.COLORS['reset']) - len(self.COLORS['cyan']) - len(self.COLORS['gray'])
        
    def _start_animation(self):
        """Start progress animation."""
        if not self.enabled:
            return
            
        self.animation_running = True
        self.animation_thread = threading.Thread(target=self._animate_progress)
        self.animation_thread.daemon = True
        self.animation_thread.start()
        
    def _stop_animation(self):
        """Stop progress animation."""
        self.animation_running = False
        if self.animation_thread:
            self.animation_thread.join(timeout=1)
            
    def _animate_progress(self):
        """Animate progress display."""
        animation_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        i = 0
        
        while self.animation_running:
            if self.enabled:
                # Update animation character
                char = animation_chars[i % len(animation_chars)]
                
                # Redisplay progress with animation
                if self.last_line_length > 0:
                    print('\r' + ' ' * self.last_line_length + '\r', end='')
                    
                self._display_progress()
                print(f" {self.COLORS['yellow']}{char}{self.COLORS['reset']}", end='', flush=True)
                
            time.sleep(0.1)
            i += 1
            
    def _get_total_elapsed_time(self) -> str:
        """Get total elapsed time as formatted string."""
        if not self.start_time:
            return "0s"
            
        elapsed = datetime.now() - self.start_time
        total_seconds = int(elapsed.total_seconds())
        
        if total_seconds < 60:
            return f"{total_seconds}s"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            return f"{minutes}m {seconds}s"
        else:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return f"{hours}h {minutes}m"
            
    def _get_step_elapsed_time(self) -> float:
        """Get current step elapsed time in seconds."""
        if not self.step_start_time:
            return 0.0
            
        elapsed = datetime.now() - self.step_start_time
        return elapsed.total_seconds()
        
    def _estimate_completion_time(self) -> Optional[str]:
        """Estimate completion time based on current progress."""
        if not self.start_time or self.current_step <= 0:
            return None
            
        elapsed = datetime.now() - self.start_time
        elapsed_seconds = elapsed.total_seconds()
        
        # Calculate average time per completed step
        completed_steps = sum(1 for status in self.steps_status if status == ProgressStatus.COMPLETED)
        if completed_steps == 0:
            return None
            
        avg_time_per_step = elapsed_seconds / completed_steps
        remaining_steps = self.total_steps - completed_steps
        
        if remaining_steps <= 0:
            return "完成"
            
        eta_seconds = remaining_steps * avg_time_per_step
        
        if eta_seconds < 60:
            return f"{int(eta_seconds)}s"
        elif eta_seconds < 3600:
            minutes = int(eta_seconds // 60)
            return f"{minutes}m"
        else:
            hours = int(eta_seconds // 3600)
            minutes = int((eta_seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
