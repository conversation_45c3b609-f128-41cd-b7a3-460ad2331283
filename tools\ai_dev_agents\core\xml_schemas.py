"""
XML Schema definitions and parsing utilities for AI development workflow.
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class UserStory:
    """User story data structure."""
    id: str
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str
    domain_context: str
    
    def to_xml(self) -> ET.Element:
        """Convert to XML element."""
        story_elem = ET.Element("story")
        story_elem.set("id", self.id)
        story_elem.set("domain_context", self.domain_context)
        
        title_elem = ET.SubElement(story_elem, "title")
        title_elem.text = self.title
        
        desc_elem = ET.SubElement(story_elem, "description")
        desc_elem.text = self.description
        
        criteria_elem = ET.SubElement(story_elem, "acceptance_criteria")
        for criterion in self.acceptance_criteria:
            criterion_elem = ET.SubElement(criteria_elem, "criterion")
            criterion_elem.text = criterion
            
        priority_elem = ET.SubElement(story_elem, "priority")
        priority_elem.text = self.priority
        
        return story_elem
    
    @classmethod
    def from_xml(cls, element: ET.Element) -> 'UserStory':
        """Create from XML element."""
        story_id = element.get("id", "")
        domain_context = element.get("domain_context", "")
        
        title = element.find("title")
        title_text = title.text if title is not None else ""
        
        description = element.find("description")
        desc_text = description.text if description is not None else ""
        
        priority = element.find("priority")
        priority_text = priority.text if priority is not None else "medium"
        
        criteria_elem = element.find("acceptance_criteria")
        criteria = []
        if criteria_elem is not None:
            for criterion in criteria_elem.findall("criterion"):
                if criterion.text:
                    criteria.append(criterion.text)
        
        return cls(
            id=story_id,
            title=title_text,
            description=desc_text,
            acceptance_criteria=criteria,
            priority=priority_text,
            domain_context=domain_context
        )


@dataclass
class FunctionalRequirement:
    """Functional requirement data structure."""
    id: str
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str
    
    def to_xml(self) -> ET.Element:
        """Convert to XML element."""
        req_elem = ET.Element("requirement")
        req_elem.set("id", self.id)
        req_elem.set("priority", self.priority)
        
        title_elem = ET.SubElement(req_elem, "title")
        title_elem.text = self.title
        
        desc_elem = ET.SubElement(req_elem, "description")
        desc_elem.text = self.description
        
        criteria_elem = ET.SubElement(req_elem, "acceptance_criteria")
        for criterion in self.acceptance_criteria:
            criterion_elem = ET.SubElement(criteria_elem, "criterion")
            criterion_elem.text = criterion
            
        return req_elem
    
    @classmethod
    def from_xml(cls, element: ET.Element) -> 'FunctionalRequirement':
        """Create from XML element."""
        req_id = element.get("id", "")
        priority = element.get("priority", "medium")
        
        title = element.find("title")
        title_text = title.text if title is not None else ""
        
        description = element.find("description")
        desc_text = description.text if description is not None else ""
        
        criteria_elem = element.find("acceptance_criteria")
        criteria = []
        if criteria_elem is not None:
            for criterion in criteria_elem.findall("criterion"):
                if criterion.text:
                    criteria.append(criterion.text)
        
        return cls(
            id=req_id,
            title=title_text,
            description=desc_text,
            acceptance_criteria=criteria,
            priority=priority
        )


@dataclass
class BusinessAnalysis:
    """Business analysis result data structure."""
    project_name: str
    project_description: str
    objectives: List[str]
    functional_requirements: List[FunctionalRequirement]
    user_stories: List[UserStory]
    generated_at: datetime
    
    def to_xml(self) -> ET.Element:
        """Convert to XML element."""
        root = ET.Element("business_analysis")
        root.set("generated_at", self.generated_at.isoformat())
        
        # Project info
        project_info = ET.SubElement(root, "project_info")
        
        name_elem = ET.SubElement(project_info, "name")
        name_elem.text = self.project_name
        
        desc_elem = ET.SubElement(project_info, "description")
        desc_elem.text = self.project_description
        
        objectives_elem = ET.SubElement(project_info, "objectives")
        for objective in self.objectives:
            obj_elem = ET.SubElement(objectives_elem, "objective")
            obj_elem.text = objective
        
        # Functional requirements
        func_reqs = ET.SubElement(root, "functional_requirements")
        for req in self.functional_requirements:
            func_reqs.append(req.to_xml())
        
        # User stories
        user_stories = ET.SubElement(root, "user_stories")
        for story in self.user_stories:
            user_stories.append(story.to_xml())
        
        return root
    
    @classmethod
    def from_xml(cls, element: ET.Element) -> 'BusinessAnalysis':
        """Create from XML element."""
        generated_at_str = element.get("generated_at", "")
        try:
            generated_at = datetime.fromisoformat(generated_at_str)
        except ValueError:
            generated_at = datetime.now()
        
        # Project info
        project_info = element.find("project_info")
        project_name = ""
        project_description = ""
        objectives = []
        
        if project_info is not None:
            name_elem = project_info.find("name")
            project_name = name_elem.text if name_elem is not None else ""
            
            desc_elem = project_info.find("description")
            project_description = desc_elem.text if desc_elem is not None else ""
            
            objectives_elem = project_info.find("objectives")
            if objectives_elem is not None:
                for obj_elem in objectives_elem.findall("objective"):
                    if obj_elem.text:
                        objectives.append(obj_elem.text)
        
        # Functional requirements
        functional_requirements = []
        func_reqs = element.find("functional_requirements")
        if func_reqs is not None:
            for req_elem in func_reqs.findall("requirement"):
                functional_requirements.append(FunctionalRequirement.from_xml(req_elem))
        
        # User stories
        user_stories = []
        stories_elem = element.find("user_stories")
        if stories_elem is not None:
            for story_elem in stories_elem.findall("story"):
                user_stories.append(UserStory.from_xml(story_elem))
        
        return cls(
            project_name=project_name,
            project_description=project_description,
            objectives=objectives,
            functional_requirements=functional_requirements,
            user_stories=user_stories,
            generated_at=generated_at
        )


class XMLParser:
    """Utility class for XML parsing and validation."""
    
    @staticmethod
    def parse_business_analysis(xml_content: str) -> BusinessAnalysis:
        """Parse business analysis from XML content."""
        try:
            root = ET.fromstring(xml_content)
            return BusinessAnalysis.from_xml(root)
        except ET.ParseError as e:
            raise ValueError(f"Invalid XML format: {e}")
    
    @staticmethod
    def extract_user_stories_from_xml(xml_content: str) -> List[UserStory]:
        """Extract user stories from XML content."""
        try:
            root = ET.fromstring(xml_content)
            user_stories = []

            # Look for user_stories_analysis structure first
            if root.tag == "user_stories_analysis":
                # Parse domain contexts structure
                domain_contexts = root.find("domain_contexts")
                if domain_contexts is not None:
                    for context_elem in domain_contexts.findall("context"):
                        stories_elem = context_elem.find("stories")
                        if stories_elem is not None:
                            for story_elem in stories_elem.findall("story"):
                                # Add domain context to story
                                story = UserStory.from_xml(story_elem)
                                story.domain_context = context_elem.get("name", "")
                                user_stories.append(story)
            else:
                # Look for traditional user_stories element
                stories_elem = root.find(".//user_stories")
                if stories_elem is not None:
                    for story_elem in stories_elem.findall("story"):
                        user_stories.append(UserStory.from_xml(story_elem))

            return user_stories
        except ET.ParseError as e:
            raise ValueError(f"Invalid XML format: {e}")
    
    @staticmethod
    def parse_user_stories_analysis(xml_content: str) -> Dict[str, Any]:
        """Parse complete user stories analysis from XML content."""
        try:
            # Try to fix common XML issues before parsing
            xml_content = XMLParser._fix_xml_issues(xml_content)
            root = ET.fromstring(xml_content)

            if root.tag != "user_stories_analysis":
                raise ValueError(f"Expected user_stories_analysis root, got {root.tag}")

            result = {
                "generated_at": root.get("generated_at", ""),
                "domain_contexts": [],
                "user_stories": [],
                "story_dependencies": []
            }

            # Parse domain contexts
            domain_contexts_elem = root.find("domain_contexts")
            if domain_contexts_elem is not None:
                for context_elem in domain_contexts_elem.findall("context"):
                    context_name = context_elem.get("name", "")
                    context_desc = context_elem.find("description")
                    context_desc_text = context_desc.text if context_desc is not None else ""

                    context_data = {
                        "name": context_name,
                        "description": context_desc_text,
                        "stories": []
                    }

                    # Parse stories in this context
                    stories_elem = context_elem.find("stories")
                    if stories_elem is not None:
                        for story_elem in stories_elem.findall("story"):
                            story_data = XMLParser._parse_story_element(story_elem, context_name)
                            context_data["stories"].append(story_data)
                            result["user_stories"].append(story_data)

                    result["domain_contexts"].append(context_data)

            # Parse story dependencies
            dependencies_elem = root.find("story_dependencies")
            if dependencies_elem is not None:
                for dep_elem in dependencies_elem.findall("dependency"):
                    dep_data = {
                        "from": dep_elem.get("from", ""),
                        "to": dep_elem.get("to", ""),
                        "type": dep_elem.get("type", ""),
                        "description": dep_elem.text or ""
                    }
                    result["story_dependencies"].append(dep_data)

            return result

        except ET.ParseError as e:
            raise ValueError(f"Invalid XML format: {e}")

    @staticmethod
    def _parse_story_element(story_elem, domain_context: str = "") -> Dict[str, Any]:
        """Parse a single story element."""
        story_data = {
            "id": story_elem.get("id", ""),
            "priority": story_elem.get("priority", "medium"),
            "domain_context": domain_context,
            "title": "",
            "description": "",
            "acceptance_criteria": [],
            "business_value": "",
            "technical_notes": ""
        }

        # Parse story fields
        title_elem = story_elem.find("title")
        if title_elem is not None:
            story_data["title"] = title_elem.text or ""

        desc_elem = story_elem.find("description")
        if desc_elem is not None:
            story_data["description"] = desc_elem.text or ""

        # Parse acceptance criteria
        criteria_elem = story_elem.find("acceptance_criteria")
        if criteria_elem is not None:
            for criterion_elem in criteria_elem.findall("criterion"):
                if criterion_elem.text:
                    story_data["acceptance_criteria"].append(criterion_elem.text)

        # Parse business value
        value_elem = story_elem.find("business_value")
        if value_elem is not None:
            story_data["business_value"] = value_elem.text or ""

        # Parse technical notes
        notes_elem = story_elem.find("technical_notes")
        if notes_elem is not None:
            story_data["technical_notes"] = notes_elem.text or ""

        return story_data

    @staticmethod
    def _fix_xml_issues(xml_content: str) -> str:
        """Fix common XML formatting issues."""
        import re

        # Remove markdown code blocks if present
        xml_content = re.sub(r'```xml\s*', '', xml_content)
        xml_content = re.sub(r'```\s*$', '', xml_content)

        # Fix unclosed story tags - look for technical_notes followed by stories or context closing
        # This specifically fixes the issue where </story> tag is missing after </technical_notes>
        pattern = r'(</technical_notes>\s*)\n(\s*</stories>)'
        xml_content = re.sub(pattern, r'\1\n                </story>\2', xml_content)

        # Fix other common unclosed tags
        patterns_to_fix = [
            (r'(</acceptance_criteria>\s*)\n(\s*</stories>)', r'\1\n                </story>\2'),
            (r'(</business_value>\s*)\n(\s*</stories>)', r'\1\n                </story>\2'),
            (r'(</description>\s*)\n(\s*</stories>)', r'\1\n                </story>\2'),
        ]

        for pattern, replacement in patterns_to_fix:
            xml_content = re.sub(pattern, replacement, xml_content)

        return xml_content.strip()

    @staticmethod
    def validate_xml_structure(xml_content: str, expected_root: str) -> bool:
        """Validate XML structure."""
        try:
            root = ET.fromstring(xml_content)
            return root.tag == expected_root
        except ET.ParseError:
            return False
    
    @staticmethod
    def extract_xml_from_text(text: str) -> Optional[str]:
        """Extract XML content from mixed text."""
        import re
        
        # Look for XML content between <xml> tags or standalone XML
        xml_patterns = [
            r'<xml>(.*?)</xml>',
            r'(<user_stories_analysis.*?</user_stories_analysis>)',
            r'(<business_analysis.*?</business_analysis>)',
            r'(<domain_model.*?</domain_model>)',
            r'(<user_stories.*?</user_stories>)',
            r'(<[^>]+>.*?</[^>]+>)'
        ]
        
        for pattern in xml_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                return matches[0]
        
        return None
