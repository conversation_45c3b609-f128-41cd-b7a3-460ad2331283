{"domain_contexts": [{"name": "订单上下文", "description": "负责订单创建、状态管理和履约流程", "stories": [{"id": "US-001", "title": "创建新订单", "description": "作为顾客，我希望能够创建新订单，以便完成商品购买", "acceptance_criteria": ["系统必须验证购物车中至少有一个有效商品", "订单创建成功后应生成唯一订单ID", "订单初始状态必须为\"待支付\""], "priority": "high", "domain_context": "订单上下文", "business_value": "实现核心下单功能，支撑电商交易流程", "technical_notes": "需要调用OrderProcessingService.place_order方法"}, {"id": "US-002", "title": "添加订单项", "description": "作为顾客，我希望能够向订单中添加商品项，以便购买多种商品", "acceptance_criteria": ["相同商品不能重复添加", "商品数量必须大于0", "订单总金额应自动重新计算"], "priority": "high", "domain_context": "订单上下文", "business_value": "支持多商品购买场景", "technical_notes": "实现Order.add_item方法"}, {"id": "US-003", "title": "应用优惠券", "description": "作为顾客，我希望能够为订单应用优惠券，以便享受折扣", "acceptance_criteria": ["每个订单只能使用一个优惠券", "优惠券必须在有效期内", "折扣金额不得超过订单总额的30%"], "priority": "medium", "domain_context": "订单上下文", "business_value": "提升用户购买转化率", "technical_notes": "实现Order.apply_discount方法"}, {"id": "US-004", "title": "取消订单", "description": "作为顾客，我希望能够取消未支付订单，以便调整购买决策", "acceptance_criteria": ["仅允许取消状态为\"待支付\"的订单", "取消后应释放预占库存", "订单状态应变为\"已取消\""], "priority": "high", "domain_context": "订单上下文", "business_value": "提供灵活的订单管理能力", "technical_notes": "需要调用OrderProcessingService.cancel_order方法"}]}, {"name": "支付上下文", "description": "处理支付流程和资金结算", "stories": [{"id": "US-005", "title": "发起支付", "description": "作为顾客，我希望能够为订单发起支付，以便完成交易", "acceptance_criteria": ["仅允许支付状态为\"待支付\"的订单", "支付金额必须与订单总额一致", "支付成功后订单状态应更新为\"已支付\""], "priority": "high", "domain_context": "支付上下文", "business_value": "实现资金流转的核心功能", "technical_notes": "需要监听OrderCreated事件"}, {"id": "US-006", "title": "处理退款", "description": "作为客服人员，我希望能够处理订单退款，以便解决售后问题", "acceptance_criteria": ["仅允许退款状态为\"已支付\"的订单", "退款金额不能超过原支付金额", "退款后订单状态应更新为\"已退款\""], "priority": "medium", "domain_context": "支付上下文", "business_value": "完善售后服务体系", "technical_notes": "需要监听OrderStatusChanged事件"}]}], "user_stories": [{"id": "US-001", "title": "创建新订单", "description": "作为顾客，我希望能够创建新订单，以便完成商品购买", "acceptance_criteria": ["系统必须验证购物车中至少有一个有效商品", "订单创建成功后应生成唯一订单ID", "订单初始状态必须为\"待支付\""], "priority": "high", "domain_context": "订单上下文", "business_value": "实现核心下单功能，支撑电商交易流程", "technical_notes": "需要调用OrderProcessingService.place_order方法"}, {"id": "US-002", "title": "添加订单项", "description": "作为顾客，我希望能够向订单中添加商品项，以便购买多种商品", "acceptance_criteria": ["相同商品不能重复添加", "商品数量必须大于0", "订单总金额应自动重新计算"], "priority": "high", "domain_context": "订单上下文", "business_value": "支持多商品购买场景", "technical_notes": "实现Order.add_item方法"}, {"id": "US-003", "title": "应用优惠券", "description": "作为顾客，我希望能够为订单应用优惠券，以便享受折扣", "acceptance_criteria": ["每个订单只能使用一个优惠券", "优惠券必须在有效期内", "折扣金额不得超过订单总额的30%"], "priority": "medium", "domain_context": "订单上下文", "business_value": "提升用户购买转化率", "technical_notes": "实现Order.apply_discount方法"}, {"id": "US-004", "title": "取消订单", "description": "作为顾客，我希望能够取消未支付订单，以便调整购买决策", "acceptance_criteria": ["仅允许取消状态为\"待支付\"的订单", "取消后应释放预占库存", "订单状态应变为\"已取消\""], "priority": "high", "domain_context": "订单上下文", "business_value": "提供灵活的订单管理能力", "technical_notes": "需要调用OrderProcessingService.cancel_order方法"}, {"id": "US-005", "title": "发起支付", "description": "作为顾客，我希望能够为订单发起支付，以便完成交易", "acceptance_criteria": ["仅允许支付状态为\"待支付\"的订单", "支付金额必须与订单总额一致", "支付成功后订单状态应更新为\"已支付\""], "priority": "high", "domain_context": "支付上下文", "business_value": "实现资金流转的核心功能", "technical_notes": "需要监听OrderCreated事件"}, {"id": "US-006", "title": "处理退款", "description": "作为客服人员，我希望能够处理订单退款，以便解决售后问题", "acceptance_criteria": ["仅允许退款状态为\"已支付\"的订单", "退款金额不能超过原支付金额", "退款后订单状态应更新为\"已退款\""], "priority": "medium", "domain_context": "支付上下文", "business_value": "完善售后服务体系", "technical_notes": "需要监听OrderStatusChanged事件"}], "story_dependencies": [{"from": "US-001", "to": "US-002", "type": "prerequisite", "description": "必须先创建订单才能添加订单项"}, {"from": "US-001", "to": "US-005", "type": "prerequisite", "description": "必须先创建订单才能发起支付"}, {"from": "US-001", "to": "US-004", "type": "prerequisite", "description": "必须先创建订单才能取消订单"}, {"from": "US-005", "to": "US-006", "type": "prerequisite", "description": "必须先支付才能退款"}], "generated_at": "2025-06-26T20:57:38.488407"}