# LLM Provider Configuration Guide

## Overview

The AI development workflow tool supports multiple LLM providers through a flexible configuration system. You can easily add new providers by updating the `config.yaml` file.

## Supported Provider Types

### 1. OpenAI-Compatible Providers (Recommended)
Most modern LLM providers use OpenAI-compatible APIs, making them easy to integrate:

- **OpenRouter** - Unified API gateway for multiple models
- **DeepSeek** - Chinese AI company with competitive models  
- **Groq** - High-speed inference platform
- **Together AI** - Open-source model hosting
- **Perplexity** - Search-augmented AI models
- **OpenAI** - Original ChatGPT provider

### 2. Native API Providers
Some providers use their own API format:
- **Anthropic** - Claude models (requires special handling)

## Configuration Structure

### Basic Provider Configuration

```yaml
llm:
  # Provider name (can be any identifier)
  your_provider_name:
    api_key: "your-api-key-here"
    base_url: "https://api.yourprovider.com/v1"  # Optional, auto-detected for known providers
    timeout: 60

# Model presets that use the provider
model_presets:
  your_model_preset:
    provider: "your_provider_name"  # Must match the provider name above
    model: "model-name-from-provider"
    temperature: 0.05
    max_tokens: 8000
    description: "Your model description"
    category: "通用"
```

### Example: Adding a New Provider

Let's say you want to add support for "Mistral AI":

```yaml
llm:
  # Add your new provider
  mistral:
    api_key: "your-mistral-api-key"
    base_url: "https://api.mistral.ai/v1"
    timeout: 60

model_presets:
  # Add model presets using the new provider
  mistral_large:
    provider: "mistral"
    model: "mistral-large-latest"
    temperature: 0.05
    max_tokens: 8000
    description: "Mistral Large Model"
    category: "通用"
```

## Auto-Detection Features

The system automatically handles:

1. **Default Base URLs**: For known providers, base_url is auto-detected if not specified
2. **Default Headers**: Provider-specific headers are added automatically
3. **OpenAI Compatibility**: Most providers work out-of-the-box with OpenAI-compatible API

## Currently Auto-Detected Providers

| Provider | Default Base URL | Special Headers |
|----------|------------------|-----------------|
| openai | https://api.openai.com/v1 | None |
| openrouter | https://openrouter.ai/api/v1 | HTTP-Referer, X-Title |
| deepseek | https://api.deepseek.com/v1 | None |
| groq | https://api.groq.com/openai/v1 | None |
| together | https://api.together.xyz/v1 | None |
| perplexity | https://api.perplexity.ai | None |

## Adding Unknown Providers

For providers not in the auto-detection list:

1. **Specify base_url**: Always include the full API endpoint
2. **Test compatibility**: Most OpenAI-compatible APIs will work
3. **Check documentation**: Verify the provider uses OpenAI-style requests

Example for a hypothetical provider:

```yaml
llm:
  custom_provider:
    api_key: "your-key"
    base_url: "https://api.customprovider.com/v1"
    timeout: 60

model_presets:
  custom_model:
    provider: "custom_provider"
    model: "their-model-name"
    temperature: 0.05
    max_tokens: 8000
    description: "Custom Provider Model"
    category: "通用"
```

## Environment Variables

You can use environment variables for API keys:

```yaml
llm:
  deepseek:
    api_key: "${DEEPSEEK_API_KEY}"  # Will read from environment
    base_url: "https://api.deepseek.com/v1"
```

## Troubleshooting

### Common Issues

1. **"Unsupported provider" error**: This error should no longer occur with the updated system
2. **Connection timeout**: Increase the `timeout` value in provider configuration
3. **Authentication failed**: Verify your API key is correct and has sufficient credits
4. **Model not found**: Check the exact model name in the provider's documentation

### Testing New Providers

Use this Python snippet to test a new provider configuration:

```python
from utils.config_manager import ConfigManager

config_manager = ConfigManager('config.yaml')
llm = config_manager.create_llm('your_preset_name')

if llm:
    print("✅ Provider created successfully!")
    print(f"Model: {llm.model_name}")
    print(f"Base URL: {llm.openai_api_base}")
else:
    print("❌ Provider creation failed")
```

## Best Practices

1. **Use descriptive provider names**: Choose clear, memorable names
2. **Set appropriate timeouts**: Consider the provider's typical response time
3. **Test with small requests first**: Verify connectivity before running full workflows
4. **Keep API keys secure**: Use environment variables for production deployments
5. **Monitor token limits**: Different models have different context length limits

## Future Enhancements

The system is designed to be easily extensible. Future improvements may include:

- Native support for Anthropic's Claude API
- Automatic model discovery from provider APIs
- Provider-specific optimization settings
- Fallback provider chains for reliability
