#!/usr/bin/env python3
"""
Test script to verify streaming workflow functionality with DeepSeek.
"""

import sys
import os
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.ai_dev_agents.utils.config_manager import ConfigManager
from tools.ai_dev_agents.utils.stream_displayer import StreamDisplayer
from tools.ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent


def test_streaming_workflow():
    """Test streaming workflow with DeepSeek."""
    print("🧪 Testing Streaming Workflow with DeepSeek...")
    
    try:
        # Initialize config manager
        config_manager = ConfigManager('config.yaml')
        
        # Create LLM with deepseek_chat preset
        print("📡 Creating LLM client...")
        llm = config_manager.create_llm('deepseek_chat')
        
        if llm is None:
            print("❌ Failed to create LLM client")
            return False
            
        print("✅ LLM client created successfully")
        
        # Create stream displayer
        stream_displayer = StreamDisplayer(enabled=True)
        
        # Create business analyzer agent with streaming
        print("🤖 Creating Business Analyzer Agent...")
        agent = BusinessAnalyzerAgent(
            llm_client=llm,
            verbose=True,
            streaming=True,
            stream_displayer=stream_displayer
        )
        
        # Test data
        test_prd = """
        # Test PRD
        
        ## Project Overview
        This is a test project for validating streaming functionality.
        
        ## Features
        - User registration
        - User login
        - Data management
        """
        
        test_rules = """
        # Test Rules
        - Use FastAPI framework
        - Follow DDD principles
        - Use UUID for primary keys
        """
        
        # Test streaming LLM call
        print("\n🚀 Testing streaming LLM call...")
        start_time = time.time()
        
        try:
            # Create test messages
            messages = [
                {"role": "system", "content": "You are a business analyst. Analyze the given PRD and provide a brief summary."},
                {"role": "user", "content": f"PRD Content:\n{test_prd}\n\nRules:\n{test_rules}\n\nPlease provide a brief analysis."}
            ]
            
            response = agent._execute_llm_call_with_streaming(messages, "测试业务分析")
            end_time = time.time()
            
            print(f"\n✅ Streaming test successful!")
            print(f"   Duration: {end_time - start_time:.2f} seconds")
            print(f"   Response length: {len(response)} characters")
            return True
            
        except Exception as api_error:
            end_time = time.time()
            print(f"❌ Streaming test failed after {end_time - start_time:.2f} seconds")
            print(f"   Error: {api_error}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("DeepSeek Streaming Workflow Test")
    print("=" * 60)
    
    success = test_streaming_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! Streaming workflow is working correctly.")
    else:
        print("💥 Tests failed. Please check the configuration.")
    print("=" * 60)
    
    sys.exit(0 if success else 1)
