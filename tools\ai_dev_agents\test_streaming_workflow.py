#!/usr/bin/env python3
"""
Test script to verify streaming workflow functionality with DeepSeek.
"""

import sys
import os
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.ai_dev_agents.utils.config_manager import ConfigManager
from tools.ai_dev_agents.utils.stream_displayer import StreamDisplayer
from tools.ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent


def test_streaming_workflow():
    """Test streaming workflow with progress display integration."""
    print("🧪 Testing Streaming Workflow with Progress Display...")

    try:
        # Initialize config manager
        config_manager = ConfigManager('config.yaml')

        # Create LLM with deepseek_chat preset
        print("📡 Creating LLM client...")
        llm = config_manager.create_llm('deepseek_chat')

        if llm is None:
            print("❌ Failed to create LLM client")
            return False

        print("✅ LLM client created successfully")

        # Import progress display and orchestrator
        from utils.progress_display import EnhancedProgressDisplay
        from core.orchestrator import Orchestrator
        from agents.business_analyzer import BusinessAnalyzerAgent

        # Create agents (without stream_displayer)
        print("🤖 Creating agents...")
        agents = {
            "business_analyzer": BusinessAnalyzerAgent(
                llm_client=llm,
                verbose=True,
                streaming=True
            )
        }

        # Create orchestrator with progress display
        print("🎛️ Creating orchestrator...")
        orchestrator = Orchestrator(
            agents=agents,
            verbose=True,
            streaming_enabled=True,
            show_progress=True
        )

        # Test data
        test_prd = """
        # Test PRD

        ## Project Overview
        This is a test project for validating streaming functionality.

        ## Features
        - User registration
        - User login
        - Data management
        """

        test_rules = """
        # Test Rules
        - Use FastAPI framework
        - Follow DDD principles
        - Use UUID for primary keys
        """

        # Test streaming with progress display
        print("\n🚀 Testing streaming with progress display...")
        start_time = time.time()

        try:
            # Test just the business analysis step
            result = orchestrator._execute_business_analysis(test_prd, test_rules)
            end_time = time.time()

            print(f"\n✅ Streaming test successful!")
            print(f"   Duration: {end_time - start_time:.2f} seconds")
            print(f"   Success: {result.success}")
            if result.success:
                print(f"   Response length: {len(str(result.data))} characters")
            return True

        except Exception as api_error:
            end_time = time.time()
            print(f"❌ Streaming test failed after {end_time - start_time:.2f} seconds")
            print(f"   Error: {api_error}")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("DeepSeek Streaming Workflow Test")
    print("=" * 60)
    
    success = test_streaming_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! Streaming workflow is working correctly.")
    else:
        print("💥 Tests failed. Please check the configuration.")
    print("=" * 60)
    
    sys.exit(0 if success else 1)
