[tool.uv]
cache-dir = ".uv_cache"

[project]
name = "ai4se_mcp_hub"
version = "0.1.0"
requires-python = ">=3.12"
dependencies = [
    "fastapi==0.115.13",
    "uvicorn==0.34.3",
    "python-dotenv==1.1.0",
    "sqlalchemy==2.0.41",
    "alembic==1.16.2",
    "passlib[bcrypt]>=1.7.4",
    "pydantic[email]==2.11.7",
    "psycopg2>=2.9.10",
    "python-multipart>=0.0.20",
    "pytest>=8.4.1",
    "httpx>=0.28.1",
    "pydantic-settings>=2.10.0",
    "pytest-cov>=6.2.1",
    "pytest-env>=1.1.5",
    "bcrypt>=4.3.0",
    "pyjwt>=2.10.1",
    "authlib>=1.6.0",
    "uuid-utils>=0.11.0",
    "requests>=2.32.4",
    "pyyaml>=6.0.2",
    "langchain-openai>=0.3.25",
    "langchain>=0.3.26",
    "langchain-anthropic>=0.3.15",
    "langchain-community>=0.3.26",
    "rich>=14.0.0",
    "textual>=3.5.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "autoflake>=2.0.0",
    "mypy>=1.7.0",
]

[dependency-groups]
dev = [
    "autoflake>=2.3.1",
    "black>=25.1.0",
    "invoke>=2.2.0",
    "isort>=6.0.1",
    "mypy>=1.16.1",
    "ruff>=0.12.0",
]

# Ruff configuration - Modern Python linter and formatter
[tool.ruff]
line-length = 88
target-version = "py312"

# Exclude files and directories
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    ".uv_cache",
    "alembic/versions",
    "build",
    "dist",
]

[tool.ruff.lint]
# Enable specific rule sets
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "N",   # pep8-naming
    "S",   # flake8-bandit (security)
    "T20", # flake8-print
    "SIM", # flake8-simplify
]

# Ignore specific rules
ignore = [
    "E501",  # line too long (handled by formatter)
    "E402",  # module level import not at top of file (needed for dynamic imports)
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert (common in tests)
    "S603",  # subprocess call (needed for code optimization script)
    "T201",  # print statements (useful for debugging)
    "N999",  # invalid module name (project name with hyphens)
]

# Per-file ignores
[tool.ruff.lint.per-file-ignores]
"tests/**/*.py" = ["S105", "S106"]  # Ignore hardcoded password warnings in tests
"scripts/**/*.py" = ["S105", "S106"]  # Ignore hardcoded password warnings in scripts
"modules/auth/interfaces/*_schemas.py" = ["S105"]  # Ignore hardcoded password warnings for OAuth2 token_type
"modules/auth/interfaces/*_api.py" = ["S106"]  # Ignore hardcoded password warnings for OAuth2 token_type

[tool.ruff.lint.isort]
known-first-party = ["modules", "common", "tests"]
known-third-party = ["fastapi", "sqlalchemy", "pydantic", "pytest", "uvicorn", "alembic"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.ruff.format]
quote-style = "double"
skip-magic-trailing-comma = false
line-ending = "auto"

# Black configuration - Code formatter
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.venv
  | \.uv_cache
  | __pycache__
  | alembic/versions
  | build
  | dist
)/
'''

# isort configuration - Import sorting
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["modules", "common", "tests"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic", "pytest", "uvicorn", "alembic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
skip_glob = ["alembic/versions/*", ".uv_cache/*"]

# MyPy configuration - Type checking
[tool.mypy]
python_version = "3.12"
namespace_packages = true
explicit_package_bases = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Per-module options
[[tool.mypy.overrides]]
module = [
    "alembic.*",
    "psycopg2.*",
    "passlib.*",
    "bcrypt.*",
]
ignore_missing_imports = true
