"""
Enhanced Configuration Manager for AI Development Agents

Provides comprehensive configuration management with features:
- YAML and JSON configuration support
- Environment variable substitution
- Configuration validation and schema checking
- Hot reloading capabilities
- Environment-specific configurations
- Configuration caching and performance optimization
- Secure credential handling
"""

import os
import re
import json
import logging
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Callable, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

try:
    import watchdog
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    # Create dummy classes for when watchdog is not available
    class FileSystemEventHandler:
        def on_modified(self, event):
            pass

    class Observer:
        def schedule(self, *args, **kwargs):
            pass
        def start(self):
            pass
        def stop(self):
            pass
        def join(self):
            pass


class ConfigFormat(Enum):
    """Supported configuration file formats."""
    YAML = "yaml"
    JSON = "json"
    AUTO = "auto"


class ValidationLevel(Enum):
    """Configuration validation levels."""
    STRICT = "strict"
    MODERATE = "moderate"
    LENIENT = "lenient"


class ConfigError(Exception):
    """Configuration-related errors."""
    pass


class ValidationError(ConfigError):
    """Configuration validation errors."""
    pass


@dataclass
class LLMConfig:
    """Enhanced LLM configuration with validation and defaults."""
    provider: str
    api_key: str
    base_url: str
    model: str
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 60
    headers: Optional[Dict[str, str]] = None
    retry_attempts: int = 3
    retry_delay: float = 1.0
    streaming: bool = False
    max_retries: int = 3
    request_timeout: int = 300

    def __post_init__(self):
        """Validate configuration after initialization."""
        self.validate()

    def validate(self) -> None:
        """Validate LLM configuration."""
        if not self.provider:
            raise ValidationError("Provider cannot be empty")

        if not self.api_key:
            raise ValidationError("API key cannot be empty")

        if self.temperature < 0 or self.temperature > 2:
            raise ValidationError("Temperature must be between 0 and 2")

        if self.max_tokens <= 0:
            raise ValidationError("Max tokens must be positive")

        if self.timeout <= 0:
            raise ValidationError("Timeout must be positive")


@dataclass
class SystemConfig:
    """System-wide configuration."""
    log_level: str = "INFO"
    verbose: bool = True
    debug: bool = False
    max_workers: int = 4
    cache_enabled: bool = True
    cache_ttl: int = 3600
    metrics_enabled: bool = True
    health_check_interval: int = 30

    def __post_init__(self):
        """Validate system configuration."""
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise ValidationError(f"Invalid log level: {self.log_level}")


@dataclass
class ProjectConfig:
    """Project-specific configuration."""
    architecture_style: str = "DDD + FastAPI"
    tech_stack: List[str] = field(default_factory=lambda: ["FastAPI", "SQLAlchemy", "Pydantic"])
    existing_modules: List[str] = field(default_factory=lambda: ["auth", "user"])
    coding_standards: Dict[str, Any] = field(default_factory=dict)
    quality_gates: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate project configuration."""
        if not self.architecture_style:
            raise ValidationError("Architecture style cannot be empty")


@dataclass
class SecurityConfig:
    """Security-related configuration."""
    encrypt_credentials: bool = True
    credential_rotation_days: int = 90
    allowed_hosts: List[str] = field(default_factory=list)
    rate_limiting: Dict[str, Any] = field(default_factory=dict)
    audit_logging: bool = True


class ConfigWatcher(FileSystemEventHandler):
    """File system watcher for configuration hot reloading."""

    def __init__(self, config_manager: 'ConfigManager'):
        self.config_manager = config_manager
        self.logger = logging.getLogger("config.watcher")

    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory and event.src_path == str(self.config_manager.config_path):
            self.logger.info(f"Configuration file changed: {event.src_path}")
            self.config_manager._reload_config()


class ConfigManager:
    """
    Enhanced configuration manager with advanced features.

    Features:
    - Multi-format support (YAML, JSON)
    - Hot reloading with file watching
    - Configuration validation and schema checking
    - Environment-specific configurations
    - Configuration caching and performance optimization
    - Secure credential handling
    - Configuration change callbacks
    """

    def __init__(
        self,
        config_path: Optional[Union[str, Path]] = None,
        format: ConfigFormat = ConfigFormat.AUTO,
        validation_level: ValidationLevel = ValidationLevel.MODERATE,
        enable_hot_reload: bool = False,
        enable_caching: bool = True
    ):
        """
        Initialize enhanced configuration manager.

        Args:
            config_path: Path to configuration file
            format: Configuration file format
            validation_level: Validation strictness level
            enable_hot_reload: Enable automatic config reloading
            enable_caching: Enable configuration caching
        """
        self.logger = logging.getLogger("config.manager")
        self.format = format
        self.validation_level = validation_level
        self.enable_hot_reload = enable_hot_reload
        self.enable_caching = enable_caching

        # Configuration state
        self.config = {}
        self.config_cache = {}
        self.last_modified = None
        self.change_callbacks: List[Callable[[Dict[str, Any]], None]] = []

        # Thread safety
        self._lock = threading.RLock()

        # File watching
        self.observer = None
        self.watcher = None

        # Determine config file path
        if config_path is None:
            config_path = self._find_default_config()
        else:
            config_path = Path(config_path)

        self.config_path = config_path

        # Load initial configuration
        self._load_config()

        # Setup hot reloading if enabled
        if self.enable_hot_reload and WATCHDOG_AVAILABLE:
            self._setup_hot_reload()

    def _find_default_config(self) -> Path:
        """Find default configuration file."""
        base_path = Path(__file__).parent.parent

        # Try different file names and formats
        candidates = [
            base_path / "config.yaml",
            base_path / "config.yml",
            base_path / "config.json",
            base_path / "ai_dev_config.yaml",
            base_path / "ai_dev_config.json"
        ]

        for candidate in candidates:
            if candidate.exists():
                return candidate

        # Return default YAML path if none found
        return base_path / "config.yaml"
    
    def _setup_hot_reload(self) -> None:
        """Setup file watching for hot reload."""
        try:
            self.watcher = ConfigWatcher(self)
            self.observer = Observer()
            self.observer.schedule(
                self.watcher,
                str(self.config_path.parent),
                recursive=False
            )
            self.observer.start()
            self.logger.info("Hot reload enabled for configuration")
        except Exception as e:
            self.logger.warning(f"Failed to setup hot reload: {e}")

    def _load_config(self) -> None:
        """Load configuration from file with enhanced format support."""
        with self._lock:
            if not self.config_path.exists():
                self.logger.warning(f"Config file not found: {self.config_path}. Using default configuration.")
                self._load_default_config()
                return

            try:
                # Determine file format
                file_format = self._detect_format()

                # Load raw configuration
                raw_config = self._load_raw_config(file_format)

                # Process environment variables
                processed_config = self._process_env_vars(raw_config)

                # Validate configuration
                if self.validation_level != ValidationLevel.LENIENT:
                    self._validate_config(processed_config)

                # Update configuration
                old_config = self.config.copy()
                self.config = processed_config
                self.last_modified = datetime.now()

                # Clear cache if caching is enabled
                if self.enable_caching:
                    self.config_cache.clear()

                # Notify change callbacks
                self._notify_config_change(old_config, self.config)

                self.logger.info(f"Configuration loaded from: {self.config_path} (format: {file_format.value})")

            except Exception as e:
                self.logger.error(f"Failed to load config: {e}")
                if self.validation_level == ValidationLevel.STRICT:
                    raise ConfigError(f"Configuration loading failed: {e}")
                else:
                    self._load_default_config()

    def _reload_config(self) -> None:
        """Reload configuration (used by hot reload)."""
        self.logger.info("Reloading configuration...")
        self._load_config()

    def _detect_format(self) -> ConfigFormat:
        """Detect configuration file format."""
        if self.format != ConfigFormat.AUTO:
            return self.format

        suffix = self.config_path.suffix.lower()
        if suffix in ['.yaml', '.yml']:
            return ConfigFormat.YAML
        elif suffix == '.json':
            return ConfigFormat.JSON
        else:
            # Default to YAML
            return ConfigFormat.YAML

    def _load_raw_config(self, file_format: ConfigFormat) -> Dict[str, Any]:
        """Load raw configuration based on format."""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            if file_format == ConfigFormat.YAML:
                if not YAML_AVAILABLE:
                    raise ConfigError("PyYAML not available for YAML configuration")
                return yaml.safe_load(f) or {}
            elif file_format == ConfigFormat.JSON:
                return json.load(f)
            else:
                raise ConfigError(f"Unsupported configuration format: {file_format}")

    def _validate_config(self, config: Dict[str, Any]) -> None:
        """Validate configuration structure and values."""
        required_sections = ["llm"]

        for section in required_sections:
            if section not in config:
                if self.validation_level == ValidationLevel.STRICT:
                    raise ValidationError(f"Required configuration section missing: {section}")
                else:
                    self.logger.warning(f"Configuration section missing: {section}")

        # Validate LLM configuration if present
        if "llm" in config:
            self._validate_llm_config(config["llm"])

    def _validate_llm_config(self, llm_config: Dict[str, Any]) -> None:
        """Validate LLM configuration section."""
        if "provider" not in llm_config:
            raise ValidationError("LLM provider not specified")

        provider = llm_config["provider"]
        if provider not in llm_config:
            raise ValidationError(f"Configuration for provider '{provider}' not found")

    def _notify_config_change(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> None:
        """Notify registered callbacks about configuration changes."""
        for callback in self.change_callbacks:
            try:
                callback(new_config)
            except Exception as e:
                self.logger.error(f"Error in config change callback: {e}")

    def register_change_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Register a callback for configuration changes."""
        self.change_callbacks.append(callback)

    def unregister_change_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Unregister a configuration change callback."""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
    
    def _load_default_config(self) -> None:
        """Load default configuration when file is not available."""
        self.config = {
            "llm": {
                "provider": "openrouter",
                "openrouter": {
                    "api_key": os.getenv("OPENROUTER_API_KEY", ""),
                    "base_url": "https://openrouter.ai/api/v1",
                    "model": "anthropic/claude-3-sonnet",
                    "temperature": 0.1,
                    "max_tokens": 4000,
                    "timeout": 60,
                    "headers": {
                        "HTTP-Referer": "https://github.com/agilemetrics-tech/ai4se-mcp-hub",
                        "X-Title": "AI4SE MCP Hub Development"
                    }
                }
            },
            "system": {
                "log_level": "INFO",
                "verbose": True,
                "retry": {
                    "max_attempts": 3,
                    "delay_seconds": 1
                }
            },
            "project": {
                "architecture_style": "DDD + FastAPI",
                "tech_stack": ["FastAPI", "SQLAlchemy", "Pydantic"],
                "existing_modules": ["auth", "user"]
            }
        }
    
    def _process_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Process environment variable substitutions in config."""
        def substitute_env_vars(obj):
            if isinstance(obj, dict):
                return {k: substitute_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [substitute_env_vars(item) for item in obj]
            elif isinstance(obj, str):
                # Replace ${VAR_NAME} with environment variable value
                pattern = r'\$\{([^}]+)\}'
                matches = re.findall(pattern, obj)
                for var_name in matches:
                    env_value = os.getenv(var_name, "")
                    obj = obj.replace(f"${{{var_name}}}", env_value)
                return obj
            else:
                return obj
        
        return substitute_env_vars(config)
    
    def get_llm_config(self, preset: Optional[str] = None) -> LLMConfig:
        """
        Get LLM configuration with caching and validation.

        Args:
            preset: Optional preset name to use instead of default config.

        Returns:
            LLMConfig object with LLM settings.
        """
        cache_key = f"llm_config_{preset or 'default'}"

        # Check cache first
        if self.enable_caching and cache_key in self.config_cache:
            return self.config_cache[cache_key]

        with self._lock:
            try:
                # Access the raw config dictionary
                raw_config = self.config if isinstance(self.config, dict) else {}

                if preset and "model_presets" in raw_config and preset in raw_config["model_presets"]:
                    # Use preset configuration
                    preset_config = raw_config["model_presets"][preset]
                    provider = preset_config["provider"]

                    # Merge preset with provider config
                    provider_config = raw_config["llm"].get(provider, {})
                    merged_config = {**provider_config, **preset_config}

                    llm_config = LLMConfig(
                        provider=provider,
                        api_key=merged_config.get("api_key", ""),
                        base_url=merged_config.get("base_url", ""),
                        model=merged_config["model"],
                        temperature=merged_config.get("temperature", 0.1),
                        max_tokens=merged_config.get("max_tokens", 4000),
                        timeout=merged_config.get("timeout", 60),
                        headers=merged_config.get("headers"),
                        retry_attempts=merged_config.get("retry_attempts", 3),
                        retry_delay=merged_config.get("retry_delay", 1.0),
                        streaming=merged_config.get("streaming", False),
                        max_retries=merged_config.get("max_retries", 3),
                        request_timeout=merged_config.get("request_timeout", 300)
                    )
                else:
                    # Use default configuration
                    llm_config = raw_config.get("llm", {})
                    provider = llm_config.get("provider", "openrouter")
                    provider_config = llm_config.get(provider, {})

                    llm_config = LLMConfig(
                        provider=provider,
                        api_key=provider_config.get("api_key", ""),
                        base_url=provider_config.get("base_url", ""),
                        model=provider_config.get("model", "anthropic/claude-3-sonnet"),
                        temperature=provider_config.get("temperature", 0.1),
                        max_tokens=provider_config.get("max_tokens", 4000),
                        timeout=provider_config.get("timeout", 60),
                        headers=provider_config.get("headers"),
                        retry_attempts=provider_config.get("retry_attempts", 3),
                        retry_delay=provider_config.get("retry_delay", 1.0),
                        streaming=provider_config.get("streaming", False),
                        max_retries=provider_config.get("max_retries", 3),
                        request_timeout=provider_config.get("request_timeout", 300)
                    )

                # Cache the result
                if self.enable_caching:
                    self.config_cache[cache_key] = llm_config

                return llm_config

            except Exception as e:
                self.logger.error(f"Error creating LLM config: {e}")
                raise ConfigError(f"Failed to create LLM configuration: {e}")

    def get_system_config(self, cli_args=None) -> SystemConfig:
        """Get system configuration with CLI argument overrides."""
        cache_key = "system_config"

        # Don't cache when CLI args are provided
        if cli_args is None and self.enable_caching and cache_key in self.config_cache:
            return self.config_cache[cache_key]

        with self._lock:
            system_data = self.config.get("system", {})

            # Default values
            log_level = system_data.get("log_level", "INFO")
            verbose = system_data.get("verbose", True)

            # Apply CLI argument overrides
            if cli_args:
                if hasattr(cli_args, 'log_level') and cli_args.log_level:
                    log_level = cli_args.log_level
                if hasattr(cli_args, 'verbose') and cli_args.verbose:
                    verbose = True

            system_config = SystemConfig(
                log_level=log_level,
                verbose=verbose,
                debug=system_data.get("debug", False),
                max_workers=system_data.get("max_workers", 4),
                cache_enabled=system_data.get("cache_enabled", True),
                cache_ttl=system_data.get("cache_ttl", 3600),
                metrics_enabled=system_data.get("metrics_enabled", True),
                health_check_interval=system_data.get("health_check_interval", 30)
            )

            # Only cache if no CLI args
            if cli_args is None and self.enable_caching:
                self.config_cache[cache_key] = system_config

            return system_config

    def get_project_config(self) -> ProjectConfig:
        """Get project configuration with validation."""
        cache_key = "project_config"

        if self.enable_caching and cache_key in self.config_cache:
            return self.config_cache[cache_key]

        with self._lock:
            project_data = self.config.get("project", {})

            project_config = ProjectConfig(
                architecture_style=project_data.get("architecture_style", "DDD + FastAPI"),
                tech_stack=project_data.get("tech_stack", ["FastAPI", "SQLAlchemy", "Pydantic"]),
                existing_modules=project_data.get("existing_modules", ["auth", "user"]),
                coding_standards=project_data.get("coding_standards", {}),
                quality_gates=project_data.get("quality_gates", {})
            )

            if self.enable_caching:
                self.config_cache[cache_key] = project_config

            return project_config

    def get_security_config(self) -> SecurityConfig:
        """Get security configuration."""
        cache_key = "security_config"

        if self.enable_caching and cache_key in self.config_cache:
            return self.config_cache[cache_key]

        with self._lock:
            security_data = self.config.get("security", {})

            security_config = SecurityConfig(
                encrypt_credentials=security_data.get("encrypt_credentials", True),
                credential_rotation_days=security_data.get("credential_rotation_days", 90),
                allowed_hosts=security_data.get("allowed_hosts", []),
                rate_limiting=security_data.get("rate_limiting", {}),
                audit_logging=security_data.get("audit_logging", True)
            )

            if self.enable_caching:
                self.config_cache[cache_key] = security_config

            return security_config
    
    def create_llm(self, preset: Optional[str] = None):
        """
        Create LLM instance based on configuration.
        
        Args:
            preset: Optional preset name to use.
            
        Returns:
            LLM instance or None if creation fails.
        """
        if not LANGCHAIN_AVAILABLE:
            self.logger.error("LangChain not available. Cannot create LLM.")
            return None
        
        config = self.get_llm_config(preset)
        
        if not config.api_key:
            self.logger.error(f"API key not found for provider: {config.provider}")
            return None
        
        try:
            if config.provider in ["openrouter", "openai"]:
                llm = ChatOpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url,
                    model=config.model,
                    temperature=config.temperature,
                    max_tokens=config.max_tokens,
                    timeout=config.timeout,
                    default_headers=config.headers or {}
                )
                self.logger.info(f"LLM created: {config.provider}/{config.model}")
                return llm
            else:
                self.logger.error(f"Unsupported provider: {config.provider}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to create LLM: {e}")
            return None
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """Get configuration for specific agent."""
        agents_config = self.config.get("agents", {})
        return agents_config.get(agent_name, {})

    def get_environment_config(self, env_name: str) -> Dict[str, Any]:
        """Get environment-specific configuration."""
        environments = self.config.get("environments", {})
        return environments.get(env_name, {})
    
    def list_available_presets(self) -> Dict[str, str]:
        """List available model presets with descriptions."""
        presets = self.config.get("model_presets", {})
        return {name: preset.get("description", "No description")
                for name, preset in presets.items()}

    def get_streaming_config(self, cli_args=None) -> Dict[str, Any]:
        """Get streaming configuration with CLI argument overrides."""
        # Default streaming configuration
        default_config = {
            'enabled': True,
            'show_thinking': True,
            'show_progress': True,
            'color_output': True,
            'buffer_size': 1
        }

        # Get config from file (if exists)
        file_config = self.config.get('streaming', {})

        # Merge with defaults
        streaming_config = {**default_config, **file_config}

        # Apply CLI argument overrides
        if cli_args:
            if hasattr(cli_args, 'no_streaming') and cli_args.no_streaming:
                streaming_config['enabled'] = False
            if hasattr(cli_args, 'no_thinking') and cli_args.no_thinking:
                streaming_config['show_thinking'] = False

        return streaming_config
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Comprehensive configuration validation.

        Returns:
            Dictionary with detailed validation results.
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "info": [],
            "validation_time": datetime.now().isoformat()
        }

        try:
            # Check LLM configuration
            llm_config = self.get_llm_config()
            if not llm_config.api_key:
                results["errors"].append(f"API key not set for provider: {llm_config.provider}")
                results["valid"] = False
            else:
                results["info"].append(f"LLM provider configured: {llm_config.provider}")

            # Check system configuration
            system_config = self.get_system_config()
            results["info"].append(f"Log level: {system_config.log_level}")

            # Check project configuration
            project_config = self.get_project_config()
            results["info"].append(f"Architecture: {project_config.architecture_style}")
            results["info"].append(f"Modules: {len(project_config.existing_modules)}")

            # Check dependencies
            if not YAML_AVAILABLE:
                results["warnings"].append("PyYAML not installed. JSON-only configuration support.")

            if not LANGCHAIN_AVAILABLE:
                results["errors"].append("LangChain not installed. Cannot create LLM instances.")
                results["valid"] = False

            if not WATCHDOG_AVAILABLE and self.enable_hot_reload:
                results["warnings"].append("Watchdog not installed. Hot reload disabled.")

            # Check file permissions
            if not os.access(self.config_path, os.R_OK):
                results["errors"].append(f"Cannot read configuration file: {self.config_path}")
                results["valid"] = False

            # Check presets
            presets = self.list_available_presets()
            if presets:
                results["info"].append(f"Available presets: {list(presets.keys())}")

        except Exception as e:
            results["errors"].append(f"Validation error: {str(e)}")
            results["valid"] = False

        return results

    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration."""
        return {
            "config_path": str(self.config_path),
            "format": self._detect_format().value,
            "last_modified": self.last_modified.isoformat() if self.last_modified else None,
            "hot_reload_enabled": self.enable_hot_reload,
            "caching_enabled": self.enable_caching,
            "validation_level": self.validation_level.value,
            "cache_size": len(self.config_cache),
            "change_callbacks": len(self.change_callbacks),
            "sections": list(self.config.keys())
        }

    def clear_cache(self) -> None:
        """Clear configuration cache."""
        with self._lock:
            self.config_cache.clear()
            self.logger.info("Configuration cache cleared")

    def export_config(self, output_path: Union[str, Path], format: ConfigFormat = ConfigFormat.YAML) -> None:
        """
        Export current configuration to file.

        Args:
            output_path: Path to export file
            format: Export format
        """
        output_path = Path(output_path)

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                if format == ConfigFormat.YAML:
                    if not YAML_AVAILABLE:
                        raise ConfigError("PyYAML not available for YAML export")
                    yaml.dump(self.config, f, default_flow_style=False, indent=2)
                elif format == ConfigFormat.JSON:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                else:
                    raise ConfigError(f"Unsupported export format: {format}")

            self.logger.info(f"Configuration exported to: {output_path}")

        except Exception as e:
            raise ConfigError(f"Failed to export configuration: {e}")

    def shutdown(self) -> None:
        """Shutdown configuration manager and cleanup resources."""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("Configuration file watcher stopped")

        self.clear_cache()
        self.change_callbacks.clear()
        self.logger.info("Configuration manager shutdown complete")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.shutdown()
