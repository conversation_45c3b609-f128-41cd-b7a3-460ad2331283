"""
Base Agent Classes and Utilities

This module provides the foundation for all AI development agents with enhanced
type safety, error handling, and monitoring capabilities.
"""

import json
import logging
import traceback
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Protocol, runtime_checkable, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

try:
    from langchain.schema import BaseMessage, HumanMessage, SystemMessage
    from langchain.callbacks.base import BaseCallbackHandler
    from langchain.schema.output import LLMResult
    from langchain.schema.language_model import BaseLanguageModel
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # Fallback for when langchain is not installed
    class BaseMessage:
        def __init__(self, content: str):
            self.content = content

    class HumanMessage(BaseMessage):
        pass

    class SystemMessage(BaseMessage):
        pass

    class BaseCallbackHandler:
        pass

    class LLMResult:
        pass

    class BaseLanguageModel:
        pass

    LANGCHAIN_AVAILABLE = False

try:
    from ..utils.config_manager import ConfigManager
    from ..utils.stream_displayer import StreamDisplayer, ContentType
    from ..utils.rules_loader import RulesLoader
except ImportError:
    # Fallback when config manager is not available
    ConfigManager = None
    StreamDisplayer = None
    ContentType = None
    RulesLoader = None


# Agent Status Enumeration
class AgentStatus(Enum):
    """Enumeration for agent execution status."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


# Exception Classes
class AgentError(Exception):
    """Base exception for agent-related errors."""
    pass


class AgentConfigError(AgentError):
    """Exception for configuration-related errors."""
    pass


class AgentExecutionError(AgentError):
    """Exception for execution-related errors."""
    pass


class AgentValidationError(AgentError):
    """Exception for validation-related errors."""
    pass


class AgentTimeoutError(AgentError):
    """Exception for timeout errors."""
    pass


# Data Classes
@dataclass
class ModuleContext:
    """Context information for a specific module."""
    name: str
    business_analysis: Optional[Dict[str, Any]] = None
    domain_model: Optional[Dict[str, Any]] = None
    requirements: Optional[Dict[str, Any]] = None
    prompt: Optional[str] = None
    generated_files: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def has_business_analysis(self) -> bool:
        """Check if business analysis is available."""
        return self.business_analysis is not None

    def has_domain_model(self) -> bool:
        """Check if domain model is available."""
        return self.domain_model is not None

    def has_requirements(self) -> bool:
        """Check if requirements are available."""
        return self.requirements is not None

    def get_context_for_step(self, step: str) -> Dict[str, Any]:
        """Get relevant context for a specific workflow step."""
        context = {"module_name": self.name}

        if step == "requirements_generation":
            if self.business_analysis:
                context["business_analysis"] = self.business_analysis
            if self.domain_model:
                context["domain_model"] = self.domain_model
        elif step == "prompt_building":
            if self.requirements:
                context["requirements"] = self.requirements
            if self.domain_model:
                context["domain_model"] = self.domain_model

        return context


@dataclass
class WorkflowContext:
    """
    Context information for the development workflow.

    This class contains all the contextual information needed by agents
    to understand the project structure and requirements.
    """
    project_root: str
    project_rules: Dict[str, Any]
    existing_modules: List[str]
    tech_stack: List[str]
    architecture_style: str
    additional_context: Dict[str, Any] = field(default_factory=dict)

    # Process context storage
    module_contexts: Dict[str, ModuleContext] = field(default_factory=dict)
    global_business_analysis: Optional[Dict[str, Any]] = None
    global_domain_model: Optional[Dict[str, Any]] = None

    def get_context_summary(self) -> str:
        """Get a summary of the workflow context."""
        return f"""
Project Context:
- Root: {self.project_root}
- Architecture: {self.architecture_style}
- Tech Stack: {', '.join(self.tech_stack)}
- Existing Modules: {', '.join(self.existing_modules)}
- Rules: {len(self.project_rules)} defined
- Module Contexts: {len(self.module_contexts)} modules tracked
"""

    def get_or_create_module_context(self, module_name: str) -> ModuleContext:
        """Get or create a module context."""
        if module_name not in self.module_contexts:
            self.module_contexts[module_name] = ModuleContext(name=module_name)
        return self.module_contexts[module_name]

    def set_global_business_analysis(self, analysis: Dict[str, Any]) -> None:
        """Set global business analysis result."""
        self.global_business_analysis = analysis

    def set_global_domain_model(self, model: Dict[str, Any]) -> None:
        """Set global domain model result."""
        self.global_domain_model = model

    def get_module_context_for_step(self, module_name: str, step: str) -> Dict[str, Any]:
        """Get module context for a specific workflow step."""
        module_ctx = self.get_or_create_module_context(module_name)
        context = module_ctx.get_context_for_step(step)

        # Add global context if available
        if step == "requirements_generation":
            if self.global_business_analysis and not context.get("business_analysis"):
                context["business_analysis"] = self.global_business_analysis
            if self.global_domain_model and not context.get("domain_model"):
                context["domain_model"] = self.global_domain_model

        return context


@dataclass
class AgentResult:
    """
    Result from an agent execution with comprehensive metadata.

    Attributes:
        success: Whether the agent execution was successful
        data: The main output data from the agent
        metadata: Additional metadata about the execution
        errors: List of error messages if any
        warnings: List of warning messages if any
        execution_time: Time taken for execution in seconds
        timestamp: When the execution completed
        status: Current status of the agent
        retry_count: Number of retries attempted
        memory_usage: Memory usage during execution (if available)
    """
    success: bool
    data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    status: AgentStatus = AgentStatus.IDLE
    retry_count: int = 0
    memory_usage: Optional[float] = None

    def add_error(self, error: str) -> None:
        """Add an error message."""
        self.errors.append(error)
        self.success = False
        self.status = AgentStatus.FAILED

    def add_warning(self, warning: str) -> None:
        """Add a warning message."""
        self.warnings.append(warning)

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "success": self.success,
            "data": self.data,
            "metadata": self.metadata,
            "errors": self.errors,
            "warnings": self.warnings,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp.isoformat(),
            "status": self.status.value,
            "retry_count": self.retry_count,
            "memory_usage": self.memory_usage
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentResult':
        """Create AgentResult from dictionary."""
        return cls(
            success=data["success"],
            data=data["data"],
            metadata=data.get("metadata", {}),
            errors=data.get("errors", []),
            warnings=data.get("warnings", []),
            execution_time=data.get("execution_time", 0.0),
            timestamp=datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())),
            status=AgentStatus(data.get("status", "idle")),
            retry_count=data.get("retry_count", 0),
            memory_usage=data.get("memory_usage")
        )


# Protocol Definitions
@runtime_checkable
class LLMProvider(Protocol):
    """Protocol for LLM providers."""
    
    def invoke(self, messages: List[BaseMessage]) -> str:
        """Invoke the LLM with messages."""
        ...
    
    def batch(self, message_lists: List[List[BaseMessage]]) -> List[str]:
        """Batch invoke the LLM."""
        ...


@runtime_checkable
class AgentMemory(Protocol):
    """Protocol for agent memory systems."""
    
    def store(self, key: str, value: Any) -> None:
        """Store a value in memory."""
        ...
    
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve a value from memory."""
        ...
    
    def clear(self) -> None:
        """Clear all memory."""
        ...


# Performance Monitoring
@dataclass
class PerformanceMetrics:
    """Performance metrics for agent execution."""
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time: float = 0.0
    memory_usage_start: Optional[float] = None
    memory_usage_end: Optional[float] = None
    llm_calls: int = 0
    tokens_used: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    
    def mark_complete(self) -> None:
        """Mark the execution as complete."""
        self.end_time = datetime.now()
        self.execution_time = (self.end_time - self.start_time).total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_time": self.execution_time,
            "memory_usage_start": self.memory_usage_start,
            "memory_usage_end": self.memory_usage_end,
            "llm_calls": self.llm_calls,
            "tokens_used": self.tokens_used,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses
        }


# Callback System
class AgentCallback(BaseCallbackHandler):
    """Enhanced callback handler for agent monitoring."""
    
    def __init__(self, agent_name: str):
        super().__init__()
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent.{agent_name}")
        self.metrics = PerformanceMetrics(start_time=datetime.now())
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """Called when LLM starts."""
        self.metrics.llm_calls += 1
        self.logger.debug(f"LLM call started for {self.agent_name}")
    
    def on_llm_end(self, response: LLMResult, **kwargs) -> None:
        """Called when LLM ends."""
        if hasattr(response, 'llm_output') and response.llm_output:
            token_usage = response.llm_output.get('token_usage', {})
            self.metrics.tokens_used += token_usage.get('total_tokens', 0)
        self.logger.debug(f"LLM call completed for {self.agent_name}")
    
    def on_llm_error(self, error: Union[Exception, KeyboardInterrupt], **kwargs) -> None:
        """Called when LLM encounters an error."""
        self.logger.error(f"LLM error in {self.agent_name}: {error}")


# Base Agent Class
class BaseAgent(ABC):
    """
    Enhanced base class for all AI development agents.
    
    Provides comprehensive error handling, monitoring, retry logic,
    and type safety for agent implementations.
    """
    
    def __init__(
        self,
        name: str,
        llm: Optional[BaseLanguageModel] = None,
        memory: Optional[AgentMemory] = None,
        config: Optional[Dict[str, Any]] = None,
        verbose: bool = False,
        max_retries: int = 3,
        timeout: float = 300.0,
        stream_displayer: Optional[StreamDisplayer] = None,
        log_dir: Optional[str] = None,
        streaming: bool = False
    ):
        """
        Initialize the base agent.

        Args:
            name: Agent name for identification
            llm: Language model instance
            memory: Memory system for the agent
            config: Configuration dictionary
            verbose: Enable verbose logging
            max_retries: Maximum number of retry attempts
            timeout: Timeout in seconds for operations
            stream_displayer: Stream displayer for real-time output
            log_dir: Directory for logging LLM interactions
            streaming: Enable streaming output
        """
        self.name = name
        self.llm = llm
        self.memory = memory
        self.config = config or {}
        self.verbose = verbose
        self.max_retries = max_retries
        self.timeout = timeout
        self.stream_displayer = stream_displayer
        self.log_dir = log_dir
        self.streaming = streaming

        # Setup logging
        self.logger = logging.getLogger(f"agent.{name}")
        if verbose:
            self.logger.setLevel(logging.DEBUG)

        # Setup callback handler
        self.callback = AgentCallback(name)

        # Agent state
        self.status = AgentStatus.IDLE
        self.current_task: Optional[str] = None
        self.metrics = PerformanceMetrics(start_time=datetime.now())

        # Initialize rules loader
        self.rules_loader = RulesLoader() if RulesLoader else None

        # Validation
        self._validate_initialization()

        # Initialize LLM interaction counter
        self._llm_interaction_count = 0
    
    def _validate_initialization(self) -> None:
        """Validate agent initialization."""
        if not self.name:
            raise AgentConfigError("Agent name cannot be empty")
        
        if self.max_retries < 0:
            raise AgentConfigError("max_retries must be non-negative")
        
        if self.timeout <= 0:
            raise AgentConfigError("timeout must be positive")
    
    def get_agent_rules(self) -> str:
        """
        Get rules specific to this agent type.

        Returns:
            Combined rule content for this agent
        """
        if not self.rules_loader:
            return ""

        # Extract agent type from class name (e.g., BusinessAnalyzerAgent -> business_analyzer)
        agent_type = self.__class__.__name__.replace('Agent', '').lower()
        # Convert CamelCase to snake_case
        import re
        agent_type = re.sub(r'(?<!^)(?=[A-Z])', '_', agent_type).lower()

        return self.rules_loader.get_rules_for_agent(agent_type)

    @abstractmethod
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """
        Process input data and return results.

        Args:
            input_data: Input data for processing
            context: Workflow context information

        Returns:
            AgentResult with processing results
        """
        pass

    def process_with_streaming(self, input_data: Dict[str, Any], context: WorkflowContext,
                             stream_callback: Optional[Callable[[str], None]] = None) -> AgentResult:
        """
        Process input data with streaming support.

        Args:
            input_data: Input data for processing
            context: Workflow context information
            stream_callback: Callback function for streaming output

        Returns:
            AgentResult with processing results
        """
        # Store the stream callback for use in LLM calls
        self._stream_callback = stream_callback

        # Call the regular process method
        result = self.process(input_data, context)

        # Clear the callback
        self._stream_callback = None

        return result
    
    def _execute_with_retry(
        self,
        operation: Callable[[], AgentResult],
        operation_name: str
    ) -> AgentResult:
        """
        Execute an operation with retry logic.
        
        Args:
            operation: Operation to execute
            operation_name: Name of the operation for logging
            
        Returns:
            AgentResult from the operation
        """
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"Executing {operation_name} (attempt {attempt + 1})")
                result = operation()
                
                if result.success:
                    if attempt > 0:
                        result.add_warning(f"Succeeded after {attempt + 1} attempts")
                    result.retry_count = attempt
                    return result
                else:
                    last_error = f"Operation failed: {result.errors}"
                    if attempt < self.max_retries:
                        self.logger.warning(f"{operation_name} failed, retrying... ({last_error})")
                    
            except Exception as e:
                last_error = str(e)
                if attempt < self.max_retries:
                    self.logger.warning(f"{operation_name} error, retrying... ({last_error})")
                else:
                    self.logger.error(f"{operation_name} failed after all retries: {last_error}")
        
        # All retries exhausted
        return AgentResult(
            success=False,
            data={},
            errors=[f"Failed after {self.max_retries + 1} attempts: {last_error}"],
            retry_count=self.max_retries,
            status=AgentStatus.FAILED
        )
    
    def _safe_json_parse(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Safely parse JSON from text with enhanced error handling.
        
        Args:
            text: Text to parse as JSON
            
        Returns:
            Parsed JSON dictionary or None if parsing fails
        """
        if not text or not text.strip():
            return None
        
        # Try to extract JSON from markdown code blocks
        import re
        json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', text, re.DOTALL)
        if json_match:
            text = json_match.group(1)
        
        # Try to find JSON object in text
        brace_start = text.find('{')
        if brace_start != -1:
            brace_count = 0
            for i, char in enumerate(text[brace_start:], brace_start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        text = text[brace_start:i+1]
                        break
        
        try:
            return json.loads(text)
        except json.JSONDecodeError as e:
            self.logger.warning(f"JSON parsing failed: {e}")
            return None
    
    def _get_memory_usage(self) -> Optional[float]:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return None
    
    def _update_status(self, status: AgentStatus, task: Optional[str] = None) -> None:
        """Update agent status and current task."""
        self.status = status
        self.current_task = task
        self.logger.debug(f"Status updated to {status.value}" + (f" - {task}" if task else ""))
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status and metrics."""
        return {
            "name": self.name,
            "status": self.status.value,
            "current_task": self.current_task,
            "metrics": self.metrics.to_dict(),
            "config": self.config
        }
    
    def reset(self) -> None:
        """Reset agent to initial state."""
        self.status = AgentStatus.IDLE
        self.current_task = None
        self.metrics = PerformanceMetrics(start_time=datetime.now())
        if self.memory:
            self.memory.clear()
        self.logger.info(f"Agent {self.name} reset to initial state")

    def _execute_llm_call_with_streaming(
        self,
        messages: List[Dict[str, str]],
        task_description: str = "LLM 处理"
    ) -> str:
        """
        Execute LLM call with streaming support.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            task_description: Description of the task for display

        Returns:
            Complete response content
        """
        if self.stream_displayer:
            self.stream_displayer.start_agent_session(self.name, task_description)

        # Log LLM interaction
        self._log_llm_interaction(messages, "request", task_description)

        def llm_operation():
            if self.llm is None:
                # Fallback to mock response if no LLM available
                return AgentResult(success=True, data="Mock response for testing")

            try:
                # Convert to LangChain messages
                lc_messages = []
                for msg in messages:
                    if msg["role"] == "system":
                        lc_messages.append(SystemMessage(content=msg["content"]))
                    elif msg["role"] == "user":
                        lc_messages.append(HumanMessage(content=msg["content"]))

                # Check if LLM supports streaming
                if hasattr(self.llm, 'stream') and (self.stream_displayer and self.stream_displayer.enabled or hasattr(self, '_stream_callback')):
                    # Use streaming
                    full_response = ""
                    for chunk in self.llm.stream(lc_messages):
                        if hasattr(chunk, 'content'):
                            chunk_content = chunk.content
                        else:
                            chunk_content = str(chunk)

                        # Send to stream displayer if available
                        if self.stream_displayer and self.stream_displayer.enabled:
                            full_response = self.stream_displayer.process_stream_chunk(chunk_content)
                        else:
                            full_response += chunk_content

                        # Send to stream callback if available
                        if hasattr(self, '_stream_callback') and self._stream_callback:
                            self._stream_callback(chunk_content)

                    return AgentResult(success=True, data=full_response)
                else:
                    # Use regular invoke
                    response = self.llm.invoke(lc_messages)
                    return AgentResult(success=True, data=response.content)

            except Exception as e:
                return AgentResult(success=False, data={}, errors=[str(e)])

        result = self._execute_with_retry(llm_operation, "LLM call")

        if result.success:
            # Log successful response
            self._log_llm_interaction(result.data, "response", task_description)
            if self.stream_displayer:
                self.stream_displayer.complete_agent_session(True, "处理成功")
            return result.data
        else:
            # Log error response
            self._log_llm_interaction(result.errors, "error", task_description)
            if self.stream_displayer:
                self.stream_displayer.complete_agent_session(False, ', '.join(result.errors))
            raise Exception(f"LLM call failed: {', '.join(result.errors)}")

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _log_llm_interaction(self, content: Any, interaction_type: str, task_description: str) -> None:
        """Log LLM interaction to file for debugging and traceability."""
        if not self.log_dir:
            return

        try:
            from pathlib import Path
            import json

            # Create log directory if it doesn't exist
            log_path = Path(self.log_dir)
            log_path.mkdir(parents=True, exist_ok=True)

            # Increment interaction counter
            self._llm_interaction_count += 1

            # Create log entry
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "agent_name": self.name,
                "interaction_id": self._llm_interaction_count,
                "task_description": task_description,
                "interaction_type": interaction_type,  # "request", "response", "error"
                "content": content
            }

            # Write to log file
            log_file = log_path / f"{self.name}_llm_interactions.jsonl"
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False, default=str) + '\n')

        except Exception as e:
            # Don't let logging errors break the main flow
            self.logger.warning(f"Failed to log LLM interaction: {e}")


# Utility Functions
def create_agent_result(
    success: bool,
    data: Dict[str, Any],
    errors: Optional[List[str]] = None,
    warnings: Optional[List[str]] = None,
    metadata: Optional[Dict[str, Any]] = None,
    execution_time: float = 0.0
) -> AgentResult:
    """
    Utility function to create AgentResult instances.
    
    Args:
        success: Whether the operation was successful
        data: Result data
        errors: List of error messages
        warnings: List of warning messages
        metadata: Additional metadata
        execution_time: Execution time in seconds
        
    Returns:
        AgentResult instance
    """
    return AgentResult(
        success=success,
        data=data,
        errors=errors or [],
        warnings=warnings or [],
        metadata=metadata or {},
        execution_time=execution_time,
        timestamp=datetime.now(),
        status=AgentStatus.COMPLETED if success else AgentStatus.FAILED
    )


def validate_input_data(
    input_data: Dict[str, Any],
    required_fields: List[str],
    optional_fields: Optional[List[str]] = None
) -> List[str]:
    """
    Validate input data against required and optional fields.
    
    Args:
        input_data: Input data to validate
        required_fields: List of required field names
        optional_fields: List of optional field names
        
    Returns:
        List of validation error messages
    """
    errors = []
    
    # Check required fields
    for field in required_fields:
        if field not in input_data:
            errors.append(f"Missing required field: {field}")
        elif input_data[field] is None:
            errors.append(f"Required field cannot be None: {field}")
        elif isinstance(input_data[field], str) and not input_data[field].strip():
            errors.append(f"Required field cannot be empty: {field}")
    
    # Check for unexpected fields
    all_allowed_fields = set(required_fields)
    if optional_fields:
        all_allowed_fields.update(optional_fields)
    
    unexpected_fields = set(input_data.keys()) - all_allowed_fields
    if unexpected_fields:
        errors.append(f"Unexpected fields: {', '.join(unexpected_fields)}")
    
    return errors
