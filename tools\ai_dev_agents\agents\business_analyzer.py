"""
Improved Business Analyzer Agent with XML output support.
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext
from ..core.xml_schemas import XMLParser, BusinessAnalysis, UserStory, FunctionalRequirement


class BusinessAnalyzerAgent(BaseAgent):
    """Agent responsible for analyzing PRD and generating XML-formatted business requirements."""
    
    def __init__(self, llm_client, verbose: bool = False, streaming: bool = False):
        """Initialize the improved business analyzer agent."""
        super().__init__(
            "improved_business_analyzer",
            llm_client,
            verbose=verbose,
            streaming=streaming
        )
        self.agent_name = "improved_business_analyzer"

        # Load system prompt
        self.system_prompt = self._load_system_prompt()
    
    def _load_system_prompt(self) -> str:
        """Load system prompt for business analysis."""
        return """你是一位资深的业务分析师，专门负责分析产品需求文档(PRD)并生成结构化的业务需求分析。

你的任务是：
1. 深度理解PRD文档的业务背景、目标和需求
2. 提取和整理功能需求和非功能需求
3. 识别核心业务流程和用户场景
4. 生成初步的用户故事框架
5. 输出XML格式的结构化业务分析报告

分析要点：
- 识别目标用户群体和使用场景
- 提取核心功能需求和业务规则
- 分析系统边界和外部依赖
- 识别关键业务流程和数据流
- 考虑非功能性需求（性能、安全、可用性等）

输出格式要求：
请严格按照以下XML格式输出分析结果：

<business_analysis generated_at="2024-01-01T00:00:00">
    <project_info>
        <name>项目名称</name>
        <description>项目描述</description>
        <objectives>
            <objective>项目目标1</objective>
            <objective>项目目标2</objective>
        </objectives>
    </project_info>
    <functional_requirements>
        <requirement id="FR-001" priority="high">
            <title>功能需求标题</title>
            <description>详细的功能需求描述</description>
            <acceptance_criteria>
                <criterion>验收标准1</criterion>
                <criterion>验收标准2</criterion>
            </acceptance_criteria>
        </requirement>
    </functional_requirements>
    <user_stories>
        <story id="US-001" domain_context="用户管理">
            <title>用户故事标题</title>
            <description>作为[角色]，我希望[功能]，以便[价值]</description>
            <acceptance_criteria>
                <criterion>验收标准1</criterion>
                <criterion>验收标准2</criterion>
            </acceptance_criteria>
            <priority>high</priority>
        </story>
    </user_stories>
</business_analysis>

请确保：
- 所有ID使用连续编号（FR-001, FR-002, US-001, US-002等）
- 优先级使用：high, medium, low
- 用户故事遵循标准格式："作为...我希望...以便..."
- 验收标准具体可测试
- 领域上下文反映业务模块划分
- 输出内容必须是有效的XML格式"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process PRD document and generate business analysis."""
        try:
            self.logger.info("Starting improved business analysis")
            
            # Extract input data
            prd_content = input_data.get("prd_content", "")
            rules_content = input_data.get("rules_content", "")
            
            if not prd_content:
                return AgentResult(
                    success=False,
                    data={},
                    errors=["No PRD content provided"]
                )
            
            self.logger.info(f"Analyzing PRD content (length: {len(prd_content)})")
            
            # Generate analysis prompt
            analysis_prompt = self._generate_analysis_prompt(prd_content, rules_content)
            
            # Get LLM response
            analysis_response = self._get_llm_response(analysis_prompt)
            if not analysis_response:
                return AgentResult(
                    success=False,
                    data={},
                    errors=["Failed to get LLM response for business analysis"]
                )
            
            # Parse XML response
            business_analysis = self._parse_analysis_response(analysis_response)
            
            # Save analysis to file
            if hasattr(context, 'output_path'):
                self._save_analysis_result(business_analysis, analysis_response, context.output_path)
            
            self.logger.info("Business analysis completed successfully")
            
            return AgentResult(
                success=True,
                data={
                    "business_analysis": business_analysis,
                    "xml_content": analysis_response,
                    "project_name": business_analysis.get("project_name", ""),
                    "user_stories_count": len(business_analysis.get("user_stories", [])),
                    "functional_requirements_count": len(business_analysis.get("functional_requirements", []))
                },
                metadata={
                    "analysis_timestamp": datetime.now().isoformat(),
                    "prd_length": len(prd_content),
                    "rules_applied": bool(rules_content)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Business analysis failed: {e}")
            return AgentResult(
                success=False,
                data={},
                errors=[str(e)]
            )
    
    def _generate_analysis_prompt(self, prd_content: str, rules_content: str) -> str:
        """Generate prompt for business analysis."""
        rules_section = ""
        if rules_content:
            rules_section = f"""
项目规则约束：
{rules_content}

请在分析过程中考虑这些规则约束，确保生成的需求和用户故事符合项目要求。
"""
        
        prompt = f"""请对以下PRD文档进行深度业务分析：

{rules_section}

PRD文档内容：
{prd_content}

请按照系统提示中的XML格式输出详细的业务分析结果。确保：
1. 项目信息准确完整
2. 功能需求覆盖PRD中的所有核心功能
3. 用户故事按照业务领域进行合理分组
4. 验收标准具体可测试
5. 优先级设置合理

请直接输出XML格式的分析结果，不要包含其他说明文字。"""
        
        return prompt
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM analysis response."""
        # Extract XML from response
        xml_content = XMLParser.extract_xml_from_text(response)
        if not xml_content:
            # Try to use the entire response as XML
            xml_content = response.strip()
        
        try:
            # Parse XML using our schema
            business_analysis = XMLParser.parse_business_analysis(xml_content)
            
            # Convert to dictionary format
            return {
                "project_name": business_analysis.project_name,
                "project_description": business_analysis.project_description,
                "objectives": business_analysis.objectives,
                "functional_requirements": [
                    {
                        "id": req.id,
                        "title": req.title,
                        "description": req.description,
                        "acceptance_criteria": req.acceptance_criteria,
                        "priority": req.priority
                    }
                    for req in business_analysis.functional_requirements
                ],
                "user_stories": [
                    {
                        "id": story.id,
                        "title": story.title,
                        "description": story.description,
                        "acceptance_criteria": story.acceptance_criteria,
                        "priority": story.priority,
                        "domain_context": story.domain_context
                    }
                    for story in business_analysis.user_stories
                ],
                "generated_at": business_analysis.generated_at.isoformat()
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to parse XML response: {e}")
            # Fallback: create a basic structure
            return {
                "project_name": "解析失败的项目",
                "project_description": "XML解析失败，需要检查LLM输出格式",
                "objectives": ["修复XML格式问题"],
                "functional_requirements": [],
                "user_stories": [],
                "generated_at": datetime.now().isoformat(),
                "parse_error": str(e),
                "raw_response": response
            }
    
    def _save_analysis_result(self, business_analysis: Dict[str, Any], 
                            xml_response: str, output_path: Path):
        """Save analysis result to files."""
        try:
            # Save structured data
            analysis_file = output_path / "business_analysis.json"
            analysis_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(business_analysis, f, indent=2, ensure_ascii=False)
            
            # Save raw XML response
            xml_file = output_path / "business_analysis.xml"
            with open(xml_file, 'w', encoding='utf-8') as f:
                f.write(xml_response)
            
            self.logger.info(f"Business analysis saved to {analysis_file} and {xml_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save business analysis: {e}")
    
    def _get_llm_response(self, prompt: str) -> Optional[str]:
        """Get response from LLM with streaming support."""
        try:
            if not self.llm:
                self.logger.warning("No LLM client available")
                return None

            # Use BaseAgent's streaming method
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ]

            response = self._execute_llm_call_with_streaming(messages, "业务分析处理")

            return response

        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return None
