"""
Result Generator Agent for creating final development documents and AI prompts.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class ResultGeneratorAgent(BaseAgent):
    """Agent responsible for generating final development documents and AI prompts."""
    
    def __init__(self, llm_client=None, verbose: bool = False, streaming: bool = False, stream_displayer=None):
        """Initialize the result generator agent."""
        super().__init__(
            "result_generator",
            llm_client,
            verbose=verbose,
            streaming=streaming,
            stream_displayer=stream_displayer
        )
        self.agent_name = "result_generator"
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Generate final development documents and AI prompts."""
        try:
            self.logger.info("Starting result generation")

            # Check if input_data is the requirements directly or contains requirements
            if "domain_contexts" in input_data:
                # input_data is the requirements data directly
                requirements = input_data
                business_analysis = {}
                domain_model = {}
                quality_review = {}
                self.logger.info("Input data is requirements directly")
            else:
                # input_data contains nested data
                business_analysis = input_data.get("business_analysis", {})
                domain_model = input_data.get("domain_model", {})
                requirements = input_data.get("requirements", {})
                quality_review = input_data.get("quality_review", {})
                self.logger.info("Input data contains nested structure")
            
            # Generate development documents
            dev_documents = self._generate_development_documents(
                business_analysis, domain_model, requirements, quality_review
            )
            
            # Generate AI prompts for each user story
            ai_prompts = self._generate_ai_prompts(requirements, business_analysis, domain_model)
            
            # Save results to files
            if hasattr(context, 'output_path'):
                self._save_results(dev_documents, ai_prompts, context.output_path)
            
            self.logger.info(f"Result generation completed. Generated {len(ai_prompts)} AI prompts")
            
            return AgentResult(
                success=True,
                data={
                    "development_documents": dev_documents,
                    "ai_prompts": ai_prompts,
                    "prompts_count": len(ai_prompts),
                    "documents_count": len(dev_documents)
                },
                metadata={
                    "generation_timestamp": datetime.now().isoformat(),
                    "total_user_stories": len(requirements.get("user_stories", []))
                }
            )
            
        except Exception as e:
            self.logger.error(f"Result generation failed: {e}")
            return AgentResult(
                success=False,
                errors=[str(e)],
                data={}
            )
    
    def _generate_development_documents(self, business_analysis: Dict[str, Any],
                                      domain_model: Dict[str, Any],
                                      requirements: Dict[str, Any],
                                      quality_review: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate development documents."""
        documents = []

        # Project overview document
        overview_doc = {
            "type": "project_overview",
            "title": f"{business_analysis.get('project_name', '项目')} - 项目概览",
            "content": self._generate_project_overview(business_analysis, quality_review),
            "filename": "01_project_overview.md"
        }
        documents.append(overview_doc)

        # Technical architecture document
        if domain_model:
            arch_doc = {
                "type": "technical_architecture",
                "title": "技术架构设计",
                "content": self._generate_architecture_document(domain_model),
                "filename": "02_technical_architecture.md"
            }
            documents.append(arch_doc)

        # Generate individual development documents for each user story
        user_stories = self._extract_user_stories_from_requirements(requirements)
        project_context = self._build_project_context(business_analysis, domain_model)

        for i, story in enumerate(user_stories, start=3):  # Start from 03 after overview and architecture
            story_doc = {
                "type": "user_story_development",
                "title": f"开发需求 - {story.get('title', story.get('id', f'Story-{i-2}'))}",
                "content": self._generate_user_story_development_document(story, project_context),
                "filename": f"{i:02d}_dev_{story.get('id', f'story-{i-2}').lower().replace('-', '_')}.md"
            }
            documents.append(story_doc)

        return documents
    
    def _generate_ai_prompts(self, requirements: Dict[str, Any],
                           business_analysis: Dict[str, Any],
                           domain_model: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate AI prompts for each user story."""
        prompts = []
        
        project_context = self._build_project_context(business_analysis, domain_model)
        
        # Extract user stories from different possible structures
        user_stories = self._extract_user_stories_from_requirements(requirements)

        for story in user_stories:
            prompt = {
                "story_id": story.get("id", ""),
                "story_title": story.get("title", ""),
                "domain_context": story.get("domain_context", ""),
                "prompt_content": self._generate_story_prompt(story, project_context),
                "filename": f"prompt_{story.get('id', 'unknown').lower()}.md"
            }
            prompts.append(prompt)
        
        return prompts

    def _extract_user_stories_from_requirements(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract user stories from requirements data structure."""
        user_stories = []

        # Debug: Log the structure of requirements
        self.logger.debug(f"Requirements keys: {list(requirements.keys())}")
        self.logger.debug(f"Requirements type: {type(requirements)}")

        # Try direct user_stories list
        if "user_stories" in requirements and isinstance(requirements["user_stories"], list):
            user_stories = requirements["user_stories"]
            self.logger.debug(f"Found direct user_stories: {len(user_stories)} stories")

        # Try domain_contexts structure
        elif "domain_contexts" in requirements:
            self.logger.debug(f"Found domain_contexts: {len(requirements['domain_contexts'])} contexts")
            for context in requirements["domain_contexts"]:
                if isinstance(context, dict) and "stories" in context:
                    stories_in_context = context["stories"]
                    self.logger.debug(f"Context '{context.get('name', 'unknown')}' has {len(stories_in_context)} stories")
                    user_stories.extend(stories_in_context)

        # Try XML parsed structure
        elif "xml_content" in requirements:
            self.logger.debug("Trying to parse XML content")
            try:
                from ..core.xml_schemas import XMLParser
                analysis_data = XMLParser.parse_user_stories_analysis(requirements["xml_content"])
                user_stories = analysis_data.get("user_stories", [])
                self.logger.debug(f"Extracted from XML: {len(user_stories)} stories")
            except Exception as e:
                self.logger.warning(f"Failed to parse XML user stories: {e}")
        else:
            self.logger.warning(f"No recognized user stories structure found in requirements")

        self.logger.info(f"Extracted {len(user_stories)} user stories for prompt generation")
        return user_stories

    def _extract_domain_contexts_from_requirements(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract domain contexts from requirements data structure."""
        domain_contexts = []

        # Debug: Log the structure of requirements
        self.logger.debug(f"Extracting domain contexts from requirements with keys: {list(requirements.keys())}")

        # Try direct domain_contexts list
        if "domain_contexts" in requirements and isinstance(requirements["domain_contexts"], list):
            domain_contexts = requirements["domain_contexts"]
            self.logger.debug(f"Found direct domain_contexts: {len(domain_contexts)} contexts")

        # Try XML parsed structure
        elif "xml_content" in requirements:
            self.logger.debug("Trying to parse XML content for domain contexts")
            try:
                from ..core.xml_schemas import XMLParser
                analysis_data = XMLParser.parse_user_stories_analysis(requirements["xml_content"])
                domain_contexts = analysis_data.get("domain_contexts", [])
                self.logger.debug(f"Extracted from XML: {len(domain_contexts)} contexts")
            except Exception as e:
                self.logger.warning(f"Failed to parse XML domain contexts: {e}")

        self.logger.info(f"Extracted {len(domain_contexts)} domain contexts for document generation")
        return domain_contexts

    def _generate_project_overview(self, business_analysis: Dict[str, Any],
                                 quality_review: Dict[str, Any]) -> str:
        """Generate project overview content."""
        content = f"""# {business_analysis.get('project_name', '项目')} - 项目概览

## 项目描述
{business_analysis.get('project_description', '无项目描述')}

## 项目目标
"""
        
        for i, objective in enumerate(business_analysis.get('objectives', []), 1):
            content += f"{i}. {objective}\n"
        
        content += "\n## 功能需求概览\n"
        for req in business_analysis.get('functional_requirements', []):
            content += f"- **{req.get('id', '')}**: {req.get('title', '')} ({req.get('priority', 'medium')})\n"
        
        if quality_review:
            content += f"\n## 质量评估\n"
            content += f"- **整体评分**: {quality_review.get('overall_score', 'N/A')}/10\n"
            content += f"- **审核状态**: {'✅ 通过' if quality_review.get('approved', False) else '❌ 需改进'}\n"
            content += f"- **评估总结**: {quality_review.get('summary', '无总结')}\n"
        
        content += f"\n## 文档生成信息\n"
        content += f"- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += f"- **生成工具**: AI开发工作流系统\n"
        
        return content
    
    def _generate_architecture_document(self, domain_model: Dict[str, Any]) -> str:
        """Generate technical architecture document."""
        content = """# 技术架构设计

## 架构概览
本项目采用领域驱动设计(DDD)架构，基于FastAPI和SQLAlchemy构建。

## 领域模型
"""
        
        # Add domain model information
        content += "```json\n"
        content += json.dumps(domain_model, ensure_ascii=False, indent=2)
        content += "\n```\n"
        
        content += """
## 技术栈
- **Web框架**: FastAPI
- **数据验证**: Pydantic
- **ORM**: SQLAlchemy
- **数据库**: PostgreSQL/MySQL
- **认证**: JWT
- **文档**: OpenAPI/Swagger

## 架构分层
1. **接口层 (Interfaces)**: FastAPI路由和Pydantic模型
2. **应用层 (Application)**: 业务用例和应用服务
3. **领域层 (Domain)**: 核心业务逻辑和实体
4. **基础设施层 (Infrastructure)**: 数据持久化和外部服务
"""
        
        return content
    
    def _generate_user_stories_document(self, context: Dict[str, Any]) -> str:
        """Generate user stories document for a domain context."""
        context_name = context.get('name', '未知领域')
        content = f"""# {context_name} - 用户故事

## 领域描述
{context.get('description', '无描述')}

## 用户故事列表
"""
        
        for story in context.get('stories', []):
            content += f"""
### {story.get('id', '')}: {story.get('title', '')}

**描述**: {story.get('description', '')}

**优先级**: {story.get('priority', 'medium')}

**验收标准**:
"""
            for criterion in story.get('acceptance_criteria', []):
                content += f"- {criterion}\n"
            
            if story.get('business_value'):
                content += f"\n**业务价值**: {story.get('business_value')}\n"
            
            if story.get('technical_notes'):
                content += f"\n**技术要点**: {story.get('technical_notes')}\n"
            
            content += "\n---\n"
        
        return content
    
    def _build_project_context(self, business_analysis: Dict[str, Any],
                             domain_model: Dict[str, Any]) -> str:
        """Build project context for AI prompts."""
        context = f"""项目背景:
- 项目名称: {business_analysis.get('project_name', '未知项目')}
- 项目描述: {business_analysis.get('project_description', '无描述')}

技术架构:
- 基于FastAPI和DDD架构
- 使用SQLAlchemy作为ORM
- 遵循四层架构模式

项目规则:
- 严格遵循PEP 8代码规范
- 强制使用类型提示
- 所有注释和文档使用英文
- 按业务模块组织代码结构
"""
        
        return context

    def _generate_user_story_development_document(self, story: Dict[str, Any], project_context: str) -> str:
        """Generate development document for a specific user story."""
        story_id = story.get('id', 'unknown')
        story_title = story.get('title', '未知用户故事')

        content = f"""# 开发需求 - {story_title}

## 用户故事信息
- **ID**: {story_id}
- **标题**: {story_title}
- **描述**: {story.get('description', '无描述')}
- **领域上下文**: {story.get('domain_context', '未知领域')}
- **优先级**: {story.get('priority', 'medium')}

## 验收标准
"""

        for criterion in story.get('acceptance_criteria', []):
            content += f"- {criterion}\n"

        if story.get('business_value'):
            content += f"\n## 业务价值\n{story.get('business_value')}\n"

        if story.get('technical_notes'):
            content += f"\n## 技术要点\n{story.get('technical_notes')}\n"

        content += f"""
## 实现指导

### 架构要求
- 严格遵循DDD四层架构模式
- 在 `modules/{story.get('domain_context', 'unknown').lower()}/` 目录下实现
- 包含完整的接口层、应用层、领域层和基础设施层代码

### 代码规范
- 严格遵循PEP 8代码规范
- 强制使用类型提示
- 所有注释和文档使用英文
- 实体ID字段统一使用UUID类型

### 测试要求
- 编写对应的单元测试和集成测试
- 测试覆盖率要求达到80%以上
- 测试用例命名使用BDD风格

### 质量标准
- 确保代码质量高、可维护性强
- 包含适当的错误处理和日志记录
- 通过所有代码质量检查工具验证

## 项目上下文
{project_context}

## 文档生成信息
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **生成工具**: AI开发工作流系统
- **用户故事ID**: {story_id}
"""

        return content

    def _generate_story_prompt(self, story: Dict[str, Any], project_context: str) -> str:
        """Generate AI prompt for a specific user story."""
        prompt = f"""# AI开发任务提示

## 任务概述
请基于以下用户故事实现完整的功能代码。

## 用户故事
**ID**: {story.get('id', '')}
**标题**: {story.get('title', '')}
**描述**: {story.get('description', '')}
**领域上下文**: {story.get('domain_context', '')}
**优先级**: {story.get('priority', 'medium')}

## 验收标准
"""
        
        for criterion in story.get('acceptance_criteria', []):
            prompt += f"- {criterion}\n"
        
        prompt += f"""
## 项目上下文
{project_context}

## 实现要求
1. 严格遵循DDD四层架构
2. 在 `modules/{story.get('domain_context', 'unknown').lower()}/` 目录下实现
3. 包含完整的接口层、应用层、领域层和基础设施层代码
4. 编写对应的单元测试和集成测试
5. 确保代码符合项目规范和质量标准

## 输出要求
请生成以下文件:
- 接口层: API路由和Pydantic模型
- 应用层: 应用服务和用例
- 领域层: 实体和仓库接口
- 基础设施层: 仓库实现和ORM模型
- 测试文件: 单元测试和集成测试

请确保代码质量高、可维护性强，并包含适当的错误处理和日志记录。
"""
        
        return prompt
    
    def _save_results(self, dev_documents: List[Dict[str, Any]],
                     ai_prompts: List[Dict[str, Any]], output_path: Path):
        """Save results to files."""
        try:
            # Create directories
            docs_dir = output_path / "development_documents"
            prompts_dir = output_path / "ai_prompts"
            docs_dir.mkdir(parents=True, exist_ok=True)
            prompts_dir.mkdir(parents=True, exist_ok=True)
            
            # Save development documents
            for doc in dev_documents:
                doc_file = docs_dir / doc["filename"]
                with open(doc_file, 'w', encoding='utf-8') as f:
                    f.write(doc["content"])
            
            # Save AI prompts
            for prompt in ai_prompts:
                prompt_file = prompts_dir / prompt["filename"]
                with open(prompt_file, 'w', encoding='utf-8') as f:
                    f.write(prompt["prompt_content"])
            
            # Save summary
            summary = {
                "generation_timestamp": datetime.now().isoformat(),
                "documents_generated": len(dev_documents),
                "prompts_generated": len(ai_prompts),
                "documents": [{"type": doc["type"], "title": doc["title"], "filename": doc["filename"]} for doc in dev_documents],
                "prompts": [{"story_id": p["story_id"], "title": p["story_title"], "filename": p["filename"]} for p in ai_prompts]
            }
            
            summary_file = output_path / "generation_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Results saved to {output_path}")
            self.logger.info(f"  - Development documents: {len(dev_documents)}")
            self.logger.info(f"  - AI prompts: {len(ai_prompts)}")
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
