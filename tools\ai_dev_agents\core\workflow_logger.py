"""
Enhanced Workflow Logger for AI Development Workflow

Provides comprehensive logging capabilities for the 6-step workflow,
including detailed step tracking, sub-task nodes, LLM interactions,
file operations, and execution summaries.
"""

import json
import logging
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional


class WorkflowLogger:
    """Enhanced logger for workflow execution tracking."""

    def __init__(self, output_path: Path, verbose: bool = False, move_system_log: bool = True):
        """Initialize enhanced workflow logger."""
        self.output_path = output_path
        self.verbose = verbose
        self.log_file = output_path / "workflow_execution.log"
        self.json_log_file = output_path / "workflow_execution.json"
        self.system_log_file = output_path / "ai_dev_workflow.log"

        # Create output directory
        output_path.mkdir(parents=True, exist_ok=True)

        # Move system log to output directory if requested
        if move_system_log:
            self._move_system_log()

        # Setup workflow-specific logger
        self.logger = logging.getLogger("workflow_execution")
        self.logger.setLevel(logging.DEBUG)

        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # File handler for workflow log
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO if verbose else logging.WARNING)

        # Enhanced formatter with more details
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Enhanced JSON log data structure
        self.json_log_data = {
            "workflow_start": datetime.now().isoformat(),
            "workflow_id": f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "steps": [],
            "summary": {},
            "system_info": self._get_system_info(),
            "configuration": {}
        }

        # Current step tracking
        self.current_step_data = None

    def _move_system_log(self):
        """Move the system log file to the output directory."""
        try:
            # Look for the system log in the current directory
            system_log_path = Path("ai_dev_workflow.log")
            if system_log_path.exists():
                shutil.move(str(system_log_path), str(self.system_log_file))
                self.logger.info(f"Moved system log to: {self.system_log_file}")
        except Exception as e:
            self.logger.warning(f"Failed to move system log: {e}")

    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for logging."""
        import platform
        import sys

        return {
            "python_version": sys.version,
            "platform": platform.platform(),
            "hostname": platform.node(),
            "timestamp": datetime.now().isoformat()
        }

    def log_step_start(self, step_number: int, step_name: str, input_data: Dict[str, Any] = None):
        """Log the start of a workflow step with enhanced details."""
        step_data = {
            "step_number": step_number,
            "step_name": step_name,
            "start_time": datetime.now().isoformat(),
            "status": "started",
            "input_summary": self._summarize_data(input_data) if input_data else {},
            "input_details": input_data if input_data else {},
            "output_summary": {},
            "output_details": {},
            "sub_tasks": [],
            "llm_interactions": [],
            "file_operations": [],
            "errors": [],
            "warnings": [],
            "duration_seconds": 0
        }

        self.json_log_data["steps"].append(step_data)
        self.current_step_data = step_data

        self.logger.info(f"=== STEP {step_number}: {step_name} STARTED ===")
        self.logger.info(f"Step description: {step_name}")

        if input_data:
            self.logger.debug(f"Input data summary: {self._summarize_data(input_data)}")
            if self.verbose:
                self.logger.debug(f"Full input data: {json.dumps(input_data, indent=2, ensure_ascii=False)}")

    def log_sub_task_start(self, step_number: int, task_name: str, description: str = ""):
        """Log the start of a sub-task within a step."""
        if not self.current_step_data:
            return

        sub_task_data = {
            "task_name": task_name,
            "description": description,
            "start_time": datetime.now().isoformat(),
            "status": "started",
            "details": [],
            "duration_seconds": 0
        }

        self.current_step_data["sub_tasks"].append(sub_task_data)

        self.logger.info(f"  → Sub-task started: {task_name}")
        if description:
            self.logger.debug(f"    Description: {description}")

    def log_sub_task_complete(self, step_number: int, task_name: str, success: bool = True, details: str = ""):
        """Log the completion of a sub-task."""
        if not self.current_step_data:
            return

        # Find and update the sub-task
        for sub_task in self.current_step_data["sub_tasks"]:
            if sub_task["task_name"] == task_name and sub_task["status"] == "started":
                sub_task["status"] = "completed" if success else "failed"
                sub_task["end_time"] = datetime.now().isoformat()

                # Calculate duration
                start_time = datetime.fromisoformat(sub_task["start_time"])
                end_time = datetime.fromisoformat(sub_task["end_time"])
                sub_task["duration_seconds"] = (end_time - start_time).total_seconds()

                if details:
                    sub_task["details"].append(details)

                status_symbol = "✓" if success else "✗"
                self.logger.info(f"  {status_symbol} Sub-task completed: {task_name} ({sub_task['duration_seconds']:.1f}s)")
                if details:
                    self.logger.debug(f"    Details: {details}")
                break

    def log_step_progress(self, step_number: int, message: str, data: Dict[str, Any] = None):
        """Log progress within a step with enhanced details."""
        self.logger.info(f"Step {step_number} Progress: {message}")

        # Add to current step's details
        if self.current_step_data:
            progress_entry = {
                "timestamp": datetime.now().isoformat(),
                "message": message,
                "data_summary": self._summarize_data(data) if data else {}
            }

            if "progress_log" not in self.current_step_data:
                self.current_step_data["progress_log"] = []
            self.current_step_data["progress_log"].append(progress_entry)

        if data and self.verbose:
            self.logger.debug(f"Progress data: {self._summarize_data(data)}")

    def log_step_complete(self, step_number: int, output_data: Dict[str, Any] = None,
                         errors: List[str] = None, warnings: List[str] = None):
        """Log the completion of a workflow step with enhanced details."""
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            step_data["status"] = "completed" if not errors else "failed"
            step_data["end_time"] = datetime.now().isoformat()
            step_data["output_summary"] = self._summarize_data(output_data) if output_data else {}
            step_data["output_details"] = output_data if output_data else {}
            step_data["errors"] = errors or []
            step_data["warnings"] = warnings or []

            # Calculate duration
            start_time = datetime.fromisoformat(step_data["start_time"])
            end_time = datetime.fromisoformat(step_data["end_time"])
            step_data["duration_seconds"] = (end_time - start_time).total_seconds()

            # Complete any remaining sub-tasks
            for sub_task in step_data.get("sub_tasks", []):
                if sub_task["status"] == "started":
                    sub_task["status"] = "completed" if not errors else "failed"
                    sub_task["end_time"] = datetime.now().isoformat()

        # Reset current step tracking
        self.current_step_data = None

        status = "COMPLETED" if not errors else "FAILED"
        duration = step_data.get("duration_seconds", 0) if step_number <= len(self.json_log_data["steps"]) else 0

        self.logger.info(f"=== STEP {step_number}: {status} ({duration:.1f}s) ===")

        # Log sub-tasks summary
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            sub_tasks = step_data.get("sub_tasks", [])
            if sub_tasks:
                completed_tasks = sum(1 for task in sub_tasks if task["status"] == "completed")
                self.logger.info(f"  Sub-tasks completed: {completed_tasks}/{len(sub_tasks)}")

        if output_data:
            self.logger.debug(f"Output data summary: {self._summarize_data(output_data)}")
            if self.verbose:
                self.logger.debug(f"Full output data: {json.dumps(output_data, indent=2, ensure_ascii=False)}")

        if warnings:
            for warning in warnings:
                self.logger.warning(f"Step {step_number} Warning: {warning}")

        if errors:
            for error in errors:
                self.logger.error(f"Step {step_number} Error: {error}")
    
    def log_llm_interaction(self, step_number: int, agent_name: str,
                           prompt_summary: str, response_summary: str,
                           duration_seconds: float = 0, model_info: Dict[str, Any] = None,
                           full_prompt: str = None, full_response: str = None):
        """Log LLM interaction details with enhanced information."""
        interaction_data = {
            "timestamp": datetime.now().isoformat(),
            "agent_name": agent_name,
            "prompt_summary": prompt_summary,
            "response_summary": response_summary,
            "duration_seconds": duration_seconds,
            "model_info": model_info or {},
            "prompt_length": len(full_prompt) if full_prompt else len(prompt_summary),
            "response_length": len(full_response) if full_response else len(response_summary)
        }

        # Store full content if verbose mode
        if self.verbose and full_prompt:
            interaction_data["full_prompt"] = full_prompt
        if self.verbose and full_response:
            interaction_data["full_response"] = full_response

        # Add to current step
        if self.current_step_data:
            self.current_step_data["llm_interactions"].append(interaction_data)
        elif step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            step_data["llm_interactions"].append(interaction_data)

        self.logger.info(f"LLM Interaction [{agent_name}]: {prompt_summary} -> {response_summary}")

        if model_info:
            model_name = model_info.get("model", "unknown")
            self.logger.debug(f"Model: {model_name}")

        if duration_seconds > 0:
            self.logger.debug(f"LLM call duration: {duration_seconds:.2f} seconds")

        # Log content lengths
        prompt_len = len(full_prompt) if full_prompt else len(prompt_summary)
        response_len = len(full_response) if full_response else len(response_summary)
        self.logger.debug(f"Content lengths - Prompt: {prompt_len}, Response: {response_len}")
    
    def log_file_operation(self, step_number: int, operation: str, file_path: str, 
                          content_summary: str = ""):
        """Log file operations."""
        file_op_data = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "file_path": file_path,
            "content_summary": content_summary
        }
        
        # Add to current step
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            if "file_operations" not in step_data:
                step_data["file_operations"] = []
            step_data["file_operations"].append(file_op_data)
        
        self.logger.info(f"File {operation}: {file_path}")
        if content_summary:
            self.logger.debug(f"Content summary: {content_summary}")
    
    def log_workflow_complete(self, success: bool, summary_data: Dict[str, Any]):
        """Log workflow completion."""
        self.json_log_data["workflow_end"] = datetime.now().isoformat()
        self.json_log_data["success"] = success
        self.json_log_data["summary"] = summary_data
        
        # Calculate total duration
        start_time = datetime.fromisoformat(self.json_log_data["workflow_start"])
        end_time = datetime.fromisoformat(self.json_log_data["workflow_end"])
        self.json_log_data["total_duration_seconds"] = (end_time - start_time).total_seconds()
        
        # Save JSON log
        self._save_json_log()
        
        status = "COMPLETED SUCCESSFULLY" if success else "FAILED"
        self.logger.info(f"=== WORKFLOW {status} ===")
        self.logger.info(f"Total duration: {self.json_log_data['total_duration_seconds']:.2f} seconds")
        self.logger.info(f"Detailed logs saved to: {self.log_file}")
        self.logger.info(f"JSON log saved to: {self.json_log_file}")
    
    def _summarize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of data for logging."""
        if not data:
            return {}
        
        summary = {}
        for key, value in data.items():
            if isinstance(value, str):
                summary[key] = f"String({len(value)} chars)"
            elif isinstance(value, list):
                summary[key] = f"List({len(value)} items)"
            elif isinstance(value, dict):
                summary[key] = f"Dict({len(value)} keys)"
            else:
                summary[key] = str(type(value).__name__)
        
        return summary
    
    def _save_json_log(self):
        """Save JSON log data to file."""
        try:
            with open(self.json_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.json_log_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save JSON log: {e}")
    
    def get_step_summary(self, step_number: int) -> Optional[Dict[str, Any]]:
        """Get summary of a specific step."""
        if step_number <= len(self.json_log_data["steps"]):
            return self.json_log_data["steps"][step_number - 1]
        return None
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """Get overall workflow summary."""
        return {
            "total_steps": len(self.json_log_data["steps"]),
            "completed_steps": len([s for s in self.json_log_data["steps"] if s["status"] == "completed"]),
            "failed_steps": len([s for s in self.json_log_data["steps"] if s["status"] == "failed"]),
            "total_duration": self.json_log_data.get("total_duration_seconds", 0),
            "success": self.json_log_data.get("success", False)
        }
