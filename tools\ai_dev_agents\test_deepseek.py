#!/usr/bin/env python3
"""
Test script to verify DeepSeek API connectivity and timeout configuration.
"""

import sys
import os
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config_manager import Confi<PERSON><PERSON><PERSON><PERSON>


def test_deepseek_connection():
    """Test DeepSeek API connection with timeout configuration."""
    print("🧪 Testing DeepSeek API connection...")
    
    try:
        # Initialize config manager
        config_manager = ConfigManager('config.yaml')
        
        # Create LLM with deepseek_chat preset
        print("📡 Creating LLM client...")
        llm = config_manager.create_llm('deepseek_chat')
        
        if llm is None:
            print("❌ Failed to create LLM client")
            return False
            
        print("✅ LLM client created successfully")
        print(f"   Model: {llm.model_name}")
        print(f"   Base URL: {llm.openai_api_base}")
        print(f"   Request Timeout: {getattr(llm, 'request_timeout', 'Not set')}")
        
        # Test a simple API call
        print("\n🚀 Testing API call...")
        start_time = time.time()
        
        try:
            response = llm.invoke("Hello! Please respond with just 'API test successful'.")
            end_time = time.time()
            
            print(f"✅ API call successful!")
            print(f"   Response: {response.content}")
            print(f"   Duration: {end_time - start_time:.2f} seconds")
            return True
            
        except Exception as api_error:
            end_time = time.time()
            print(f"❌ API call failed after {end_time - start_time:.2f} seconds")
            print(f"   Error: {api_error}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("DeepSeek API Connection Test")
    print("=" * 60)
    
    success = test_deepseek_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! DeepSeek API is working correctly.")
    else:
        print("💥 Tests failed. Please check the configuration and API key.")
    print("=" * 60)
    
    sys.exit(0 if success else 1)
