#!/usr/bin/env python3
"""
Enhanced TUI (Text User Interface) for AI Development Agents

Provides rich interactive interfaces using the Rich library for:
- Model selection from presets
- File selection with preview
- Clean operation confirmation with file listing
- Progress display and status updates
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.columns import Columns
from rich.tree import Tree
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.layout import Layout
from rich.live import Live
from rich.align import Align
from rich.rule import Rule


class TUIInterface:
    """Enhanced TUI interface using Rich library."""
    
    def __init__(self):
        self.console = Console()
    
    def show_banner(self):
        """Display application banner."""
        banner = Text("AI 开发工作流工具", style="bold white")
        subtitle = Text("AI Development Workflow Tool", style="dim white")

        panel = Panel(
            Align.center(f"{banner}\n{subtitle}"),
            border_style="bright_black",
            padding=(1, 2)
        )
        self.console.print(panel)
        self.console.print()
    
    def select_model_preset(self, config_manager) -> Optional[str]:
        """
        Display model presets in a rich table and let user select one.

        Args:
            config_manager: ConfigManager instance

        Returns:
            Selected preset key or None if cancelled
        """
        # Get presets from config manager
        try:
            presets = config_manager.config.get("model_presets", {})
            if not presets:
                self.console.print("[red]配置文件中没有模型预设[/red]")
                return None
        except Exception as e:
            self.console.print(f"[red]获取模型预设失败: {e}[/red]")
            return None
        self.console.print(Rule("[bold blue]模型选择 / Model Selection"))
        self.console.print()
        
        # Create table for model presets
        table = Table(
            title="可用模型预设 / Available Model Presets",
            show_header=True,
            header_style="bold white",
            border_style="bright_black"
        )

        table.add_column("编号", style="white", width=6)
        table.add_column("名称", style="bold white", width=15)
        table.add_column("类别", style="bright_black", width=12)
        table.add_column("模型", style="white", width=35)
        table.add_column("描述", style="bright_black", width=40)
        table.add_column("参数", style="dim white", width=20)
        
        preset_keys = list(presets.keys())
        
        for i, (key, preset) in enumerate(presets.items(), 1):
            provider = preset.get('provider', 'unknown')
            model = preset.get('model', 'unknown')
            description = preset.get('description', 'No description')
            category = preset.get('category', '通用')
            temperature = preset.get('temperature', 0.1)
            max_tokens = preset.get('max_tokens', 0)
            
            params = f"T:{temperature}, Max:{max_tokens//1000}k"
            
            # 简化显示，减少颜色
            table.add_row(
                str(i),
                key,
                category,
                f"[dim]{provider}[/dim]\n{model}",
                description,
                params
            )
        
        self.console.print(table)
        self.console.print()
        
        # Get user selection
        while True:
            try:
                choice = Prompt.ask(
                    "请选择模型预设编号 (1-{}) 或按 Enter 使用默认".format(len(preset_keys)),
                    default="1"
                )
                
                if choice == "":
                    return "default"
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(preset_keys):
                    selected_key = preset_keys[choice_num - 1]
                    selected_preset = presets[selected_key]
                    
                    # Show confirmation with minimal styling
                    self.console.print(f"\n✓ 已选择: [bold white]{selected_key}[/bold white]")
                    self.console.print(f"  模型: {selected_preset.get('model', 'unknown')}")
                    self.console.print(f"  描述: {selected_preset.get('description', 'No description')}")
                    self.console.print()
                    
                    return selected_key
                else:
                    self.console.print(f"[red]请输入 1-{len(preset_keys)} 之间的数字[/red]")
                    
            except ValueError:
                self.console.print("[red]请输入有效的数字[/red]")
            except KeyboardInterrupt:
                self.console.print("\n[yellow]已取消选择[/yellow]")
                return None
    
    def select_files(self, directory: Path, pattern: str, title: str, 
                    allow_multiple: bool = False, allow_skip: bool = True) -> List[Path]:
        """
        Enhanced file selection with rich display.
        
        Args:
            directory: Directory to search for files
            pattern: File pattern (e.g., "*.md")
            title: Selection title
            allow_multiple: Allow selecting multiple files
            allow_skip: Allow skipping file selection
            
        Returns:
            List of selected file paths
        """
        self.console.print(Rule(f"[bold white]{title}"))
        self.console.print()

        # Find matching files
        files = list(directory.glob(pattern))
        if not files:
            self.console.print(f"[bright_black]在 {directory} 中未找到匹配 {pattern} 的文件[/bright_black]")
            return []

        # Create table for file listing
        table = Table(
            title=f"找到 {len(files)} 个文件",
            show_header=True,
            header_style="bold white",
            border_style="bright_black"
        )

        table.add_column("编号", style="white", width=6)
        table.add_column("文件名", style="bold white", width=30)
        table.add_column("大小", style="bright_black", width=10)
        table.add_column("修改时间", style="bright_black", width=20)
        table.add_column("预览", style="dim white", width=50)
        
        for i, file_path in enumerate(files, 1):
            try:
                stat = file_path.stat()
                size = self._format_file_size(stat.st_size)
                mtime = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")
                
                # Get file preview (first line)
                preview = ""
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        preview = first_line[:47] + "..." if len(first_line) > 50 else first_line
                except:
                    preview = "[无法预览]"
                
                table.add_row(
                    str(i),
                    file_path.name,
                    size,
                    mtime,
                    preview
                )
            except Exception as e:
                table.add_row(str(i), file_path.name, "N/A", "N/A", f"[red]错误: {e}[/red]")
        
        if allow_skip:
            table.add_row("0", "[dim]跳过选择[/dim]", "", "", "")
        
        self.console.print(table)
        self.console.print()
        
        # Get user selection
        selected_files = []
        
        if allow_multiple:
            prompt_text = f"请输入文件编号 (1-{len(files)})，多个文件用逗号分隔"
            if allow_skip:
                prompt_text += "，或输入 0 跳过"
        else:
            prompt_text = f"请输入文件编号 (1-{len(files)})"
            if allow_skip:
                prompt_text += " 或输入 0 跳过"
        
        while True:
            try:
                choice = Prompt.ask(prompt_text)
                
                if choice == "0" and allow_skip:
                    break
                
                # Parse selection
                if allow_multiple:
                    choices = [int(x.strip()) for x in choice.split(',')]
                else:
                    choices = [int(choice)]
                
                # Validate choices
                valid_choices = []
                for choice_num in choices:
                    if 1 <= choice_num <= len(files):
                        valid_choices.append(choice_num)
                    else:
                        self.console.print(f"[red]编号 {choice_num} 无效，请输入 1-{len(files)} 之间的数字[/red]")
                        break
                else:
                    # All choices are valid
                    selected_files = [files[i-1] for i in valid_choices]
                    break
                    
            except ValueError:
                self.console.print("[red]请输入有效的数字[/red]")
            except KeyboardInterrupt:
                self.console.print("\n[yellow]已取消选择[/yellow]")
                break
        
        # Show confirmation
        if selected_files:
            self.console.print(f"\n✓ 已选择 {len(selected_files)} 个文件:")
            for file_path in selected_files:
                self.console.print(f"  • [green]{file_path.name}[/green]")
            self.console.print()
        
        return selected_files
    
    def confirm_clean_operation(self, output_dir: Path, days: Optional[int] = None) -> bool:
        """
        Show files to be cleaned and confirm operation.

        Args:
            output_dir: Output directory to clean
            days: Only show files older than this many days

        Returns:
            True if user confirms, False otherwise
        """
        self.console.print(Rule("[bold white]清理确认 / Clean Confirmation"))
        self.console.print()

        if not output_dir.exists():
            self.console.print(f"[bright_black]输出目录不存在: {output_dir}[/bright_black]")
            return False

        # Find files to clean
        files_to_clean = []
        total_size = 0
        cutoff_time = None

        if days:
            cutoff_time = datetime.now() - timedelta(days=days)
            self.console.print(f"[white]查找 {days} 天前的文件 (早于 {cutoff_time.strftime('%Y-%m-%d %H:%M')})[/white]")
        else:
            self.console.print("[white]查找所有输出文件[/white]")

        self.console.print()

        # Scan directory
        for item in output_dir.iterdir():
            if item.is_dir() and item.name.startswith('run_'):
                # Check if directory should be cleaned
                should_clean = True
                if days and cutoff_time:
                    try:
                        dir_mtime = datetime.fromtimestamp(item.stat().st_mtime)
                        should_clean = dir_mtime < cutoff_time
                    except:
                        pass

                if should_clean:
                    # Calculate directory size
                    dir_size = self._calculate_directory_size(item)
                    files_to_clean.append((item, dir_size, True))  # True = directory
                    total_size += dir_size
            elif item.is_file():
                # Check if file should be cleaned
                should_clean = True
                if days and cutoff_time:
                    try:
                        file_mtime = datetime.fromtimestamp(item.stat().st_mtime)
                        should_clean = file_mtime < cutoff_time
                    except:
                        pass

                if should_clean:
                    try:
                        file_size = item.stat().st_size
                        files_to_clean.append((item, file_size, False))  # False = file
                        total_size += file_size
                    except:
                        pass

        if not files_to_clean:
            self.console.print("[white]没有找到需要清理的文件[/white]")
            return False

        # Create table showing files to be cleaned
        table = Table(
            title=f"将要删除的文件 ({len(files_to_clean)} 项)",
            show_header=True,
            header_style="bold white",
            border_style="bright_black"
        )

        table.add_column("类型", style="white", width=8)
        table.add_column("名称", style="bold white", width=40)
        table.add_column("大小", style="bright_black", width=12)
        table.add_column("修改时间", style="bright_black", width=20)

        for item, size, is_dir in files_to_clean:
            try:
                mtime = datetime.fromtimestamp(item.stat().st_mtime).strftime("%Y-%m-%d %H:%M")
                item_type = "目录" if is_dir else "文件"

                table.add_row(
                    item_type,
                    item.name,
                    self._format_file_size(size),
                    mtime
                )
            except Exception as e:
                table.add_row(
                    "错误",
                    item.name,
                    "N/A",
                    f"错误: {e}"
                )

        self.console.print(table)
        self.console.print()

        # Show summary
        summary_panel = Panel(
            f"[bold white]总计: {len(files_to_clean)} 项文件/目录\n"
            f"总大小: {self._format_file_size(total_size)}[/bold white]",
            title="清理摘要",
            border_style="bright_black"
        )
        self.console.print(summary_panel)
        self.console.print()

        # Confirm deletion
        return Confirm.ask(
            "[bold white]确认删除以上文件吗？此操作不可撤销！[/bold white]",
            default=False
        )

    def _calculate_directory_size(self, directory: Path) -> int:
        """Calculate total size of directory recursively."""
        total_size = 0
        try:
            for item in directory.rglob('*'):
                if item.is_file():
                    try:
                        total_size += item.stat().st_size
                    except:
                        pass
        except:
            pass
        return total_size

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes < 1024:
            return f"{size_bytes}B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes/1024:.1f}KB"
        else:
            return f"{size_bytes/(1024*1024):.1f}MB"
