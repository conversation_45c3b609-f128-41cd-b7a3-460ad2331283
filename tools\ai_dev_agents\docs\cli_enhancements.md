# CLI 增强功能文档

本文档描述了AI开发工作流工具的命令行增强功能。

## 新增功能概览

### 1. 交互式文件选择
- **参数**: `--interactive` 或当未提供PRD文件时自动启用
- **功能**: 提供友好的文件选择界面，支持PRD文件和规则文件的交互选择
- **特性**: 
  - 支持多个规则文件选择
  - 显示文件列表和编号选择
  - 支持跳过可选文件

### 2. 日志控制系统
- **参数**: 
  - `--log-level {DEBUG,INFO,WARNING,ERROR,CRITICAL}`: 设置日志级别
  - `--console-log`: 启用控制台日志输出（默认仅输出到文件）
- **功能**: 精确控制日志输出级别和目标

### 3. 用户友好的进度显示
- **参数**: `--no-progress`: 禁用进度显示
- **功能**: 在非流式模式下显示工作流执行进度
- **特性**:
  - 实时进度条显示
  - 当前步骤名称和描述
  - 已用时间和预计完成时间
  - 动画效果指示器
  - 步骤完成状态显示

### 4. 流式输出控制
- **参数**: `--streaming`: 启用流式输出显示LLM响应过程
- **功能**: 实时显示LLM生成过程，提供更好的用户体验
- **注意**: 启用流式输出时会自动禁用进度显示

### 5. 清理功能
- **参数**: 
  - `--clean`: 清理输出目录
  - `--days DAYS`: 清理指定天数前的输出（与--clean一起使用）
- **功能**: 管理输出目录，清理旧的运行结果
- **特性**:
  - 交互式确认删除
  - 支持按时间过滤清理
  - 安全的删除操作

### 6. 多规则文件支持
- **参数**: `--rules RULES` (可多次使用)
- **功能**: 支持加载多个规则文件
- **示例**: `--rules rules1.md --rules rules2.md`

## 使用示例

### 基本用法
```bash
# 交互式选择文件
python main.py

# 指定PRD文件
python main.py input/prd.md

# 启用详细日志和控制台输出
python main.py input/prd.md --verbose --console-log

# 启用流式输出
python main.py input/prd.md --streaming

# 禁用进度显示
python main.py input/prd.md --no-progress
```

### 高级用法
```bash
# 多规则文件 + 自定义日志级别
python main.py input/prd.md --rules rules/base.md --rules rules/custom.md --log-level DEBUG

# 交互模式 + 流式输出
python main.py --interactive --streaming

# 清理输出目录
python main.py --clean
python main.py --clean --days 7  # 清理7天前的输出
```

## 技术实现

### 核心组件
1. **ProgressDisplay**: 进度显示组件，提供实时进度反馈
2. **交互式文件选择**: 基于终端的文件选择界面
3. **增强的日志系统**: 支持多级别日志控制
4. **清理系统**: 安全的文件清理功能

### 架构集成
- 所有增强功能都与现有的Agent业务逻辑完全分离
- 通过参数传递控制各种显示和交互行为
- 保持向后兼容性，所有新功能都是可选的

## 注意事项

1. **流式输出与进度显示**: 两者不能同时启用，流式输出优先级更高
2. **交互模式**: 在CI/CD环境中应避免使用交互模式
3. **清理功能**: 清理操作不可逆，请谨慎使用
4. **多规则文件**: 规则文件内容会按顺序合并，后面的规则可能覆盖前面的规则

## 配置建议

### 开发环境
```bash
python main.py --interactive --streaming --console-log --log-level DEBUG
```

### 生产环境
```bash
python main.py input/prd.md --rules rules/production.md --log-level INFO
```

### 调试模式
```bash
python main.py input/prd.md --verbose --console-log --log-level DEBUG --no-progress
```
