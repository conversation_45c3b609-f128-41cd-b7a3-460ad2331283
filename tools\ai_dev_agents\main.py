#!/usr/bin/env python3
"""
AI Development Workflow Main Entry Point

This script implements the enhanced 6-step workflow:
1. Business Analysis (XML output)
2. Domain Modeling
3. Requirements Analysis (User stories by domain context)
4. Quality Review (with retry mechanism)
5. Result Generation
6. HTML Presentation
"""

import argparse
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from tools.ai_dev_agents.core.orchestrator import Orchestrator
from tools.ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent
from tools.ai_dev_agents.agents.domain_modeler import DomainModelerAgent
from tools.ai_dev_agents.agents.requirements_analyzer import RequirementsAnalyzerAgent
from tools.ai_dev_agents.agents.technical_leader import TechnicalLeaderAgent
from tools.ai_dev_agents.agents.result_generator import ResultGeneratorAgent
from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
from tools.ai_dev_agents.utils.config_manager import ConfigManager


def setup_logging(verbose: bool = False) -> logging.Logger:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('ai_dev_workflow.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def load_input_files(prd_file: Path, rules_file: Path) -> tuple[str, str]:
    """Load PRD and rules content from files."""
    try:
        with open(prd_file, 'r', encoding='utf-8') as f:
            prd_content = f.read()
    except Exception as e:
        raise FileNotFoundError(f"Failed to read PRD file {prd_file}: {e}")

    rules_content = ""
    if rules_file and rules_file.exists():
        try:
            with open(rules_file, 'r', encoding='utf-8') as f:
                rules_content = f.read()
        except Exception as e:
            logging.warning(f"Failed to read rules file {rules_file}: {e}")

    return prd_content, rules_content


def create_agents(llm_client, verbose: bool) -> Dict[str, Any]:
    """Create all required agents for the workflow."""
    agents = {
        "business_analyzer": BusinessAnalyzerAgent(llm_client, verbose),
        "domain_modeler": DomainModelerAgent(llm_client, verbose),
        "requirements_analyzer": RequirementsAnalyzerAgent(llm_client, verbose),
        "technical_leader": TechnicalLeaderAgent(llm_client, verbose),
        "result_generator": ResultGeneratorAgent(llm_client, verbose),
        "presentation_generator": PresentationGeneratorAgent(llm_client, verbose)
    }
    return agents


def create_output_directory() -> Path:
    """Create timestamped output directory."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # Use relative path from the current script location
    script_dir = Path(__file__).parent
    output_dir = script_dir / "output" / f"run_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def print_workflow_summary(results: Dict[str, Any], logger: logging.Logger):
    """Print workflow execution summary."""
    logger.info("=" * 60)
    logger.info("AI开发工作流执行总结")
    logger.info("=" * 60)

    logger.info(f"执行状态: {'✅ 成功' if results['success'] else '❌ 失败'}")
    logger.info(f"完成步骤: {results['steps_completed']}/{results['total_steps']}")
    logger.info(f"执行时间: {results['execution_time']:.2f} 秒")

    if results['success']:
        workflow_results = results.get('results', {})

        # Business analysis summary
        business_analysis = workflow_results.get('business_analysis', {})
        if business_analysis:
            logger.info(f"项目名称: {business_analysis.get('project_name', 'N/A')}")
            logger.info(f"功能需求数量: {business_analysis.get('functional_requirements_count', 0)}")

        # Requirements summary
        requirements = workflow_results.get('requirements', {})
        if requirements:
            logger.info(f"用户故事数量: {len(requirements.get('user_stories', []))}")
            logger.info(f"领域上下文数量: {len(requirements.get('domain_contexts', []))}")

        # Quality review summary
        quality_review = workflow_results.get('quality_review', {})
        if quality_review:
            logger.info(f"质量评分: {quality_review.get('overall_score', 'N/A')}/10")
            logger.info(f"审核状态: {'通过' if quality_review.get('approved', False) else '需改进'}")

        # HTML report
        presentation = workflow_results.get('presentation', {})
        if presentation:
            logger.info(f"HTML报告: {presentation.get('html_file', 'N/A')}")

    if results.get('errors'):
        logger.error("执行错误:")
        for error in results['errors']:
            logger.error(f"  - {error}")

    logger.info("=" * 60)


def main():
    """Main entry point for the AI development workflow."""
    parser = argparse.ArgumentParser(
        description="AI开发工作流",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py input/prd.md
  python main.py input/prd.md --rules input/rules.md --verbose
  python main.py input/prd.md --output custom_output_dir
        """
    )

    parser.add_argument(
        "prd_file",
        type=Path,
        help="PRD文档文件路径"
    )

    parser.add_argument(
        "--rules",
        type=Path,
        help="项目规则文件路径（可选）"
    )

    parser.add_argument(
        "--output",
        type=Path,
        help="输出目录路径（默认自动生成时间戳目录）"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )

    parser.add_argument(
        "--config",
        type=Path,
        default=None,
        help="LLM配置文件路径"
    )

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging(args.verbose)
    logger.info("启动AI开发工作流")

    try:
        # Validate input files
        if not args.prd_file.exists():
            raise FileNotFoundError(f"PRD文件不存在: {args.prd_file}")

        # Load input content
        logger.info(f"加载PRD文件: {args.prd_file}")
        prd_content, rules_content = load_input_files(args.prd_file, args.rules)
        logger.info(f"PRD内容长度: {len(prd_content)} 字符")

        if rules_content:
            logger.info(f"规则内容长度: {len(rules_content)} 字符")
        else:
            logger.info("未提供规则文件")

        # Create output directory
        output_dir = args.output if args.output else create_output_directory()
        logger.info(f"输出目录: {output_dir}")

        # Initialize LLM client
        logger.info("初始化LLM客户端")
        config_path = args.config if args.config else "config.yaml"
        logger.info(f"配置文件路径: {config_path}")
        logger.info(f"配置文件存在: {Path(config_path).exists()}")
        config_manager = ConfigManager(config_path=config_path)
        llm_client = config_manager.create_llm()
        if llm_client is None:
            raise RuntimeError("Failed to create LLM client")

        # Create agents
        logger.info("创建工作流代理")
        agents = create_agents(llm_client, args.verbose)

        # Create orchestrator
        orchestrator = Orchestrator(agents, args.verbose)

        # Execute workflow
        logger.info("开始执行6步工作流")
        results = orchestrator.execute_workflow(prd_content, rules_content, output_dir)

        # Print summary
        print_workflow_summary(results, logger)

        # Exit with appropriate code
        sys.exit(0 if results['success'] else 1)

    except KeyboardInterrupt:
        logger.info("用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"工作流执行失败: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
