#!/usr/bin/env python3
"""
AI Development Workflow Main Entry Point

This script implements the enhanced 6-step workflow:
1. Business Analysis (XML output)
2. Domain Modeling
3. Requirements Analysis (User stories by domain context)
4. Quality Review (with retry mechanism)
5. Result Generation
6. HTML Presentation
"""

import argparse
import logging
import sys
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from tools.ai_dev_agents.core.orchestrator import Orchestrator
from tools.ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent
from tools.ai_dev_agents.agents.domain_modeler import DomainModelerAgent
from tools.ai_dev_agents.agents.requirements_analyzer import RequirementsAnalyzerAgent
from tools.ai_dev_agents.agents.technical_leader import TechnicalLeaderAgent
from tools.ai_dev_agents.agents.result_generator import ResultGeneratorAgent
from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
from tools.ai_dev_agents.utils.config_manager import ConfigManager
from tools.ai_dev_agents.utils.tui_interface import TUIInterface



def interactive_file_selection(directory: Path, file_type: str, allow_multiple: bool = False) -> List[Path]:
    """Interactive file selection using enhanced TUI interface."""
    tui = TUIInterface()

    # Determine file pattern based on file type
    if "PRD" in file_type.upper():
        pattern = "*.md"
        title = "PRD文档选择"
    elif "规则" in file_type or "rule" in file_type.lower():
        pattern = "*.md"
        title = "规则文件选择"
    else:
        pattern = "*.*"
        title = f"{file_type}文件选择"

    return tui.select_files(
        directory=directory,
        pattern=pattern,
        title=title,
        allow_multiple=allow_multiple,
        allow_skip=True
    )


def clean_output_directory(output_dir: Path, days: Optional[int] = None) -> None:
    """Clean output directory using enhanced TUI interface."""
    tui = TUIInterface()

    # Use TUI to confirm clean operation
    if tui.confirm_clean_operation(output_dir, days):
        # Perform the actual cleaning
        if days is None:
            # Clean all
            shutil.rmtree(output_dir)
            output_dir.mkdir(exist_ok=True)
            tui.console.print("[green]✓ 已清理所有输出文件[/green]")
        else:
            # Clean files older than specified days
            cutoff_date = datetime.now() - timedelta(days=days)
            cleaned_count = 0

            for item in output_dir.iterdir():
                if item.is_dir() and item.name.startswith("run_"):
                    try:
                        # Extract timestamp from directory name like "run_20250626_143244"
                        timestamp_str = item.name[4:]  # Remove "run_" prefix
                        item_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                        if item_date < cutoff_date:
                            shutil.rmtree(item)
                            cleaned_count += 1
                    except ValueError:
                        # Skip directories that don't match the expected format
                        continue
                elif item.is_file():
                    try:
                        file_mtime = datetime.fromtimestamp(item.stat().st_mtime)
                        if file_mtime < cutoff_date:
                            item.unlink()
                            cleaned_count += 1
                    except:
                        continue

            tui.console.print(f"[green]✓ 已清理 {cleaned_count} 个旧文件/目录[/green]")
    else:
        tui.console.print("[yellow]已取消清理操作[/yellow]")


def setup_logging(log_level: str = "INFO", enable_console: bool = False, output_dir: Path = None) -> logging.Logger:
    """Setup logging configuration with output directory support."""
    # Convert string level to logging level
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    level = level_map.get(log_level.upper(), logging.INFO)

    # Determine log file location
    if output_dir:
        log_file = output_dir / 'ai_dev_workflow.log'
        # Ensure output directory exists
        output_dir.mkdir(parents=True, exist_ok=True)
    else:
        log_file = Path('ai_dev_workflow.log')

    # Setup handlers
    handlers = [logging.FileHandler(log_file, encoding='utf-8')]
    if enable_console:
        handlers.append(logging.StreamHandler(sys.stdout))

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
        handlers=handlers,
        force=True  # Override any existing configuration
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Logging setup complete. Log file: {log_file}")
    return logger


def load_input_files(prd_file: Path, rules_files: List[Path]) -> tuple[str, str]:
    """Load PRD and rules content from files."""
    try:
        with open(prd_file, 'r', encoding='utf-8') as f:
            prd_content = f.read()
    except FileNotFoundError:
        raise FileNotFoundError(f"PRD文件不存在: {prd_file}")
    except PermissionError:
        raise FileNotFoundError(f"PRD文件权限不足: {prd_file}")
    except UnicodeDecodeError:
        raise FileNotFoundError(f"PRD文件编码错误，请确保文件为UTF-8编码: {prd_file}")
    except Exception as e:
        raise FileNotFoundError(f"读取PRD文件失败 {prd_file}: {e}")

    rules_content = ""
    if rules_files:
        rules_parts = []
        failed_rules = []
        for rules_file in rules_files:
            if rules_file.exists():
                try:
                    with open(rules_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        rules_parts.append(f"# Rules from {rules_file.name}\n{content}")
                except Exception as e:
                    failed_rules.append(f"{rules_file.name}: {e}")
                    logging.warning(f"读取规则文件失败 {rules_file}: {e}")
            else:
                failed_rules.append(f"{rules_file.name}: 文件不存在")
                logging.warning(f"规则文件不存在: {rules_file}")

        if failed_rules:
            logging.warning(f"部分规则文件加载失败: {', '.join(failed_rules)}")

        rules_content = "\n\n".join(rules_parts)

    return prd_content, rules_content


def create_agents(llm_client, verbose: bool, streaming_enabled: bool = False) -> Dict[str, Any]:
    """Create all required agents for the workflow."""
    # Note: We don't pass stream_displayer to agents anymore
    # All streaming output will go through orchestrator's callback system

    agents = {
        "business_analyzer": BusinessAnalyzerAgent(
            llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        ),
        "domain_modeler": DomainModelerAgent(
            llm=llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        ),
        "requirements_analyzer": RequirementsAnalyzerAgent(
            llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        ),
        "technical_leader": TechnicalLeaderAgent(
            llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        ),
        "result_generator": ResultGeneratorAgent(
            llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        ),
        "presentation_generator": PresentationGeneratorAgent(
            llm_client,
            verbose=verbose,
            streaming=streaming_enabled
        )
    }
    return agents


def create_output_directory() -> Path:
    """Create timestamped output directory."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # Use relative path from the current script location
    script_dir = Path(__file__).parent
    output_dir = script_dir / "output" / f"run_{timestamp}"
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir


def generate_file_listing(output_dir: Path) -> Dict[str, Any]:
    """Generate a comprehensive file listing of the output directory."""
    file_listing = {
        "output_directory": str(output_dir),
        "total_files": 0,
        "total_size_bytes": 0,
        "files_by_category": {},
        "detailed_files": []
    }

    # File categories
    categories = {
        "文档": [".md", ".txt", ".doc", ".docx"],
        "HTML": [".html", ".htm"],
        "JSON": [".json"],
        "日志": [".log"],
        "图片": [".png", ".jpg", ".jpeg", ".gif", ".svg"],
        "其他": []
    }

    if not output_dir.exists():
        return file_listing

    # Scan all files recursively
    for file_path in output_dir.rglob("*"):
        if file_path.is_file():
            file_size = file_path.stat().st_size
            file_ext = file_path.suffix.lower()
            relative_path = file_path.relative_to(output_dir)

            # Categorize file
            category = "其他"
            for cat_name, extensions in categories.items():
                if file_ext in extensions:
                    category = cat_name
                    break

            # Add to category
            if category not in file_listing["files_by_category"]:
                file_listing["files_by_category"][category] = {
                    "count": 0,
                    "total_size": 0,
                    "files": []
                }

            file_info = {
                "name": file_path.name,
                "path": str(relative_path),
                "size_bytes": file_size,
                "size_human": _format_file_size(file_size),
                "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            }

            file_listing["files_by_category"][category]["count"] += 1
            file_listing["files_by_category"][category]["total_size"] += file_size
            file_listing["files_by_category"][category]["files"].append(file_info)
            file_listing["detailed_files"].append(file_info)

            file_listing["total_files"] += 1
            file_listing["total_size_bytes"] += file_size

    return file_listing


def _format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"


def diagnose_common_issues(error_message: str) -> tuple[str, list[str]]:
    """Diagnose common issues and provide specific solutions."""
    error_lower = error_message.lower()

    if "token" in error_lower and "context length" in error_lower:
        return "Token上下文长度超限", [
            "当前输入内容超过了模型的最大token限制",
            "解决方案:",
            "  1. 减少PRD文档或规则文件的长度",
            "  2. 在config.yaml中降低max_tokens设置",
            "  3. 选择支持更大上下文的付费模型",
            "  4. 将长文档分段处理"
        ]
    elif "api" in error_lower or "http" in error_lower:
        return "API连接问题", [
            "无法连接到LLM服务提供商",
            "解决方案:",
            "  1. 检查网络连接是否正常",
            "  2. 验证API密钥是否正确配置",
            "  3. 确认选择的模型服务是否可用",
            "  4. 检查防火墙或代理设置"
        ]
    elif "json" in error_lower or "parse" in error_lower:
        return "数据格式解析错误", [
            "LLM返回的数据格式不符合预期",
            "解决方案:",
            "  1. 重新运行程序（LLM输出可能不稳定）",
            "  2. 尝试使用不同的模型",
            "  3. 检查系统提示是否正确",
            "  4. 使用--verbose参数查看详细输出"
        ]
    elif "permission" in error_lower or "access" in error_lower:
        return "文件权限问题", [
            "无法访问指定的文件或目录",
            "解决方案:",
            "  1. 检查文件是否存在",
            "  2. 确认文件权限设置",
            "  3. 以管理员权限运行程序",
            "  4. 检查文件是否被其他程序占用"
        ]
    elif "encoding" in error_lower or "unicode" in error_lower:
        return "文件编码问题", [
            "文件编码格式不正确",
            "解决方案:",
            "  1. 确保文件保存为UTF-8编码",
            "  2. 使用文本编辑器重新保存文件",
            "  3. 检查文件中是否有特殊字符",
            "  4. 尝试使用其他编码格式"
        ]
    else:
        return "未知错误", [
            "遇到了未预期的错误",
            "解决方案:",
            "  1. 使用--verbose参数获取详细错误信息",
            "  2. 检查日志文件中的完整错误堆栈",
            "  3. 重新启动程序",
            "  4. 如果问题持续，请联系技术支持"
        ]


def print_workflow_summary(results: Dict[str, Any], logger: logging.Logger, output_dir: Path = None):
    """Print enhanced workflow execution summary with file listing."""
    logger.info("=" * 60)
    logger.info("AI开发工作流执行总结")
    logger.info("=" * 60)

    logger.info(f"执行状态: {'✅ 成功' if results['success'] else '❌ 失败'}")
    logger.info(f"完成步骤: {results['steps_completed']}/{results['total_steps']}")
    logger.info(f"执行时间: {results['execution_time']:.2f} 秒")

    if results['success']:
        workflow_results = results.get('results', {})

        # Business analysis summary
        business_analysis = workflow_results.get('business_analysis', {})
        if business_analysis:
            logger.info(f"项目名称: {business_analysis.get('project_name', 'N/A')}")
            logger.info(f"功能需求数量: {business_analysis.get('functional_requirements_count', 0)}")

        # Requirements summary
        requirements = workflow_results.get('requirements', {})
        if requirements:
            logger.info(f"用户故事数量: {len(requirements.get('user_stories', []))}")
            logger.info(f"领域上下文数量: {len(requirements.get('domain_contexts', []))}")

        # Quality review summary
        quality_review = workflow_results.get('quality_review', {})
        if quality_review:
            logger.info(f"质量评分: {quality_review.get('overall_score', 'N/A')}/10")
            logger.info(f"审核状态: {'通过' if quality_review.get('approved', False) else '需改进'}")

        # HTML report
        presentation = workflow_results.get('presentation', {})
        if presentation:
            logger.info(f"HTML报告: {presentation.get('html_file', 'N/A')}")

    # Generate and display file listing
    if output_dir and output_dir.exists():
        logger.info("-" * 60)
        logger.info("生成文件清单")
        logger.info("-" * 60)

        file_listing = generate_file_listing(output_dir)

        logger.info(f"输出目录: {file_listing['output_directory']}")
        logger.info(f"文件总数: {file_listing['total_files']}")
        logger.info(f"总大小: {_format_file_size(file_listing['total_size_bytes'])}")

        # Display files by category
        for category, category_info in file_listing["files_by_category"].items():
            if category_info["count"] > 0:
                logger.info(f"\n{category} ({category_info['count']} 个文件, {_format_file_size(category_info['total_size'])}):")
                for file_info in category_info["files"]:
                    logger.info(f"  - {file_info['name']} ({file_info['size_human']})")

        # Save file listing to JSON
        listing_file = output_dir / "file_listing.json"
        try:
            with open(listing_file, 'w', encoding='utf-8') as f:
                import json
                json.dump(file_listing, f, indent=2, ensure_ascii=False)
            logger.info(f"\n文件清单已保存到: {listing_file}")
        except Exception as e:
            logger.warning(f"保存文件清单失败: {e}")

    if results.get('errors'):
        logger.error("\n执行错误:")

        # Handle both old string format and new structured format
        for error in results['errors']:
            if isinstance(error, dict):
                # New structured error format
                logger.error(f"  步骤 {error['step']}: {error['step_name']}")
                logger.error(f"    错误类型: {error['error_type']}")
                logger.error(f"    错误信息: {error['error_message']}")
                if error.get('suggestions'):
                    logger.error("    解决建议:")
                    for suggestion in error['suggestions']:
                        logger.error(f"      • {suggestion}")
            else:
                # Old string format (backward compatibility)
                logger.error(f"  - {error}")

        # Also print to console for immediate visibility
        print("\n" + "="*60)
        print("❌ 工作流执行失败")
        print("="*60)

        for error in results['errors']:
            if isinstance(error, dict):
                print(f"🔸 {error['step_name']} (第{error['step']}步)")
                print(f"   错误类型: {error['error_type']}")
                print(f"   错误信息: {error['error_message']}")
                if error.get('suggestions'):
                    print("   💡 解决建议:")
                    for suggestion in error['suggestions']:
                        print(f"      • {suggestion}")
                print()
            else:
                print(f"🔸 {error}")

        print("📋 详细信息请查看日志文件")
        print("="*60)

    logger.info("=" * 60)


def main():
    """Main entry point for the AI development workflow."""
    parser = argparse.ArgumentParser(
        description="AI开发工作流",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                                    # 交互式选择文件
  python main.py input/prd.md                      # 指定PRD文件
  python main.py input/prd.md --rules rules/rules.md --verbose
  python main.py --clean                           # 清理输出目录
  python main.py --clean --days 7                  # 清理7天前的输出
        """
    )

    parser.add_argument(
        "prd_file",
        type=Path,
        nargs='?',  # Make PRD file optional for interactive mode
        help="PRD文档文件路径（可选，未提供时进入交互模式）"
    )

    parser.add_argument(
        "--rules",
        type=Path,
        action='append',  # Allow multiple rules files
        help="项目规则文件路径（可指定多个）"
    )

    parser.add_argument(
        "--output",
        type=Path,
        help="输出目录路径（默认自动生成时间戳目录）"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )

    parser.add_argument(
        "--config",
        type=Path,
        default=None,
        help="LLM配置文件路径"
    )

    parser.add_argument(
        "--model",
        type=str,
        help="指定模型预设名称（跳过交互选择）"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="日志输出级别（默认: INFO）"
    )

    parser.add_argument(
        "--console-log",
        action="store_true",
        help="启用控制台日志输出（默认仅输出到文件）"
    )

    parser.add_argument(
        "--interactive",
        action="store_true",
        help="启用交互模式（自动检测未提供的文件）"
    )



    parser.add_argument(
        "--no-progress",
        action="store_true",
        help="禁用进度显示（在非流式模式下）"
    )

    parser.add_argument(
        "--clean",
        action="store_true",
        help="清理输出目录"
    )

    parser.add_argument(
        "--days",
        type=int,
        help="清理指定天数前的输出（与--clean一起使用）"
    )

    args = parser.parse_args()

    # Handle clean command
    if args.clean:
        script_dir = Path(__file__).parent
        output_dir = script_dir / "output"
        clean_output_directory(output_dir, args.days)
        return

    # Setup logging
    logger = setup_logging(args.log_level, args.console_log)
    logger.info("启动AI开发工作流")

    try:
        # Handle interactive file selection
        script_dir = Path(__file__).parent

        # PRD file selection
        prd_file = args.prd_file
        if not prd_file or args.interactive:
            if not prd_file:
                print("未指定PRD文件，进入交互选择模式...")
                input_dir = script_dir / "input"
                selected_prd = interactive_file_selection(input_dir, "PRD", allow_multiple=False)
                if not selected_prd:
                    print("未选择PRD文件，退出程序")
                    return
                prd_file = selected_prd[0]

        # Validate PRD file
        if not prd_file.exists():
            raise FileNotFoundError(f"PRD文件不存在: {prd_file}")

        # Rules files selection
        rules_files = []
        if args.rules:
            rules_files = args.rules
        elif args.interactive or not args.rules:
            print("选择规则文件...")
            rules_dir = script_dir / "rules"
            selected_rules = interactive_file_selection(rules_dir, "规则", allow_multiple=True)
            rules_files = selected_rules

        # Load input content
        logger.info(f"加载PRD文件: {prd_file}")
        prd_content, rules_content = load_input_files(prd_file, rules_files)
        logger.info(f"PRD内容长度: {len(prd_content)} 字符")

        if rules_content:
            logger.info(f"规则内容长度: {len(rules_content)} 字符")
            if rules_files:
                logger.info(f"加载的规则文件: {[f.name for f in rules_files]}")
        else:
            logger.info("未提供规则文件")

        # Create output directory
        output_dir = args.output if args.output else create_output_directory()
        logger.info(f"输出目录: {output_dir}")

        # Re-setup logging to use the output directory
        logger = setup_logging(args.log_level, args.console_log, output_dir)
        logger.info("日志系统已重新配置到输出目录")

        # Model selection
        selected_model = args.model
        if not selected_model:
            tui = TUIInterface()
            config_path = args.config if args.config else "config.yaml"
            config_manager = ConfigManager(config_path)

            # Show banner and model selection
            tui.show_banner()
            selected_model = tui.select_model_preset(config_manager)

            if not selected_model:
                tui.console.print("[yellow]未选择模型，退出程序[/yellow]")
                return

            logger.info(f"用户选择模型预设: {selected_model}")

            # Temperature mode selection
            selected_temperature_mode = tui.select_temperature_mode(config_manager)
            if not selected_temperature_mode:
                tui.console.print("[yellow]未选择温度模式，退出程序[/yellow]")
                return

            logger.info(f"用户选择温度模式: {selected_temperature_mode}")
        else:
            logger.info(f"使用指定模型预设: {selected_model}")
            # Use default temperature mode when model is specified via command line
            selected_temperature_mode = "balanced"
            logger.info(f"使用默认温度模式: {selected_temperature_mode}")

        # Initialize LLM client
        logger.info("初始化LLM客户端")
        config_path = args.config if args.config else "config.yaml"
        logger.info(f"配置文件路径: {config_path}")
        logger.info(f"使用模型预设: {selected_model}")
        logger.info(f"使用温度模式: {selected_temperature_mode}")
        logger.info(f"配置文件存在: {Path(config_path).exists()}")

        try:
            config_manager = ConfigManager(config_path=config_path)
        except Exception as e:
            raise RuntimeError(f"配置文件加载失败: {e}")

        try:
            llm_client = config_manager.create_llm(preset=selected_model, temperature_mode=selected_temperature_mode)
        except Exception as e:
            raise RuntimeError(f"LLM客户端创建失败: {e}")

        if llm_client is None:
            raise RuntimeError(f"无法创建LLM客户端，请检查模型预设 '{selected_model}' 的配置")

        # Create agents with streaming support (always enabled)
        logger.info("创建工作流代理")
        agents = create_agents(llm_client, args.verbose, streaming_enabled=True)

        # Get model name and temperature for display
        model_preset = config_manager.get_model_preset(selected_model)
        model_name = model_preset.get("model", selected_model)
        temperature_value = config_manager.get_temperature_value(selected_temperature_mode)
        model_display_info = f"{model_name} | 温度: {temperature_value}"

        # Create orchestrator with enhanced options
        orchestrator = Orchestrator(
            agents,
            args.verbose,
            streaming_enabled=True,
            show_progress=not args.no_progress,
            model_info=model_display_info
        )

        # Execute workflow
        logger.info("开始执行6步工作流")
        results = orchestrator.execute_workflow(prd_content, rules_content, output_dir)

        # Print summary with file listing
        print_workflow_summary(results, logger, output_dir)

        # Exit with appropriate code
        sys.exit(0 if results['success'] else 1)

    except KeyboardInterrupt:
        print("\n" + "="*60)
        print("🛑 用户中断执行")
        print("="*60)
        logger.info("用户中断执行")
        sys.exit(130)
    except FileNotFoundError as e:
        print("\n" + "="*60)
        print("❌ 文件错误")
        print("="*60)
        print(f"📁 错误原因: {e}")
        print("💡 解决建议:")
        print("   • 检查文件路径是否正确")
        print("   • 确认文件是否存在")
        print("   • 检查文件权限")
        print("="*60)
        logger.error(f"文件错误: {e}")
        sys.exit(1)
    except RuntimeError as e:
        print("\n" + "="*60)
        print("❌ 运行时错误")
        print("="*60)
        print(f"⚙️  错误原因: {e}")
        print("💡 解决建议:")
        if "LLM client" in str(e):
            print("   • 检查网络连接")
            print("   • 验证API密钥配置")
            print("   • 确认模型配置正确")
            print("   • 检查config.yaml文件格式")
        else:
            print("   • 检查系统配置")
            print("   • 重新启动程序")
            print("   • 查看详细日志")
        print("="*60)
        logger.error(f"运行时错误: {e}")
        sys.exit(1)
    except Exception as e:
        # Use diagnostic function for better error analysis
        error_type, suggestions = diagnose_common_issues(str(e))

        print("\n" + "="*60)
        print(f"❌ {error_type}")
        print("="*60)
        print(f"🔍 错误类型: {type(e).__name__}")
        print(f"📝 错误信息: {e}")
        print()
        for suggestion in suggestions:
            if suggestion.startswith("  "):
                print(f"   {suggestion}")
            else:
                print(f"💡 {suggestion}")
        print("="*60)

        logger.error(f"工作流执行失败: {e}")
        if args.verbose:
            import traceback
            print("\n📋 详细错误堆栈:")
            print("-" * 40)
            traceback.print_exc()
            print("-" * 40)
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
